<?php
/**
 * فحص المسارات المطلوبة لأوامر الاستلام
 * ضع هذا الملف في المجلد الجذر وشغله من المتصفح
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🔍 فحص مسارات أوامر الاستلام</h1>";
echo "<style>
body { font-family: Arial, sans-serif; margin: 20px; }
.success { color: green; }
.error { color: red; }
.warning { color: orange; }
.info { color: blue; }
.section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
table { width: 100%; border-collapse: collapse; }
th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
th { background-color: #f2f2f2; }
</style>";

// 1. فحص ملف المسارات
echo "<div class='section'>";
echo "<h2>1. فحص ملف المسارات</h2>";

if (file_exists('routes/web.php')) {
    echo "<span class='success'>✅ ملف routes/web.php موجود</span><br>";
    
    $routes_content = file_get_contents('routes/web.php');
    
    // فحص مسارات أوامر الاستلام
    $receipt_routes = [
        'receipt-order' => 'Route::resource.*receipt-order',
        'receipt-order.store' => 'receipt-order.*store',
        'receipt-order.create' => 'receipt-order.*create',
        'receipt-order.index' => 'receipt-order.*index'
    ];
    
    foreach ($receipt_routes as $route_name => $pattern) {
        if (preg_match('/' . $pattern . '/i', $routes_content)) {
            echo "<span class='success'>✅ مسار {$route_name} موجود</span><br>";
        } else {
            echo "<span class='error'>❌ مسار {$route_name} مفقود</span><br>";
        }
    }
} else {
    echo "<span class='error'>❌ ملف routes/web.php غير موجود</span><br>";
}
echo "</div>";

// 2. اختبار المسارات
echo "<div class='section'>";
echo "<h2>2. اختبار المسارات</h2>";

$base_url = (isset($_SERVER['HTTPS']) ? 'https' : 'http') . '://' . $_SERVER['HTTP_HOST'];

$test_routes = [
    'الصفحة الرئيسية لأوامر الاستلام' => $base_url . '/receipt-order',
    'صفحة إنشاء أمر جديد' => $base_url . '/receipt-order/create',
    'إرسال بيانات جديدة (POST)' => $base_url . '/receipt-order (POST)',
];

echo "<table>";
echo "<tr><th>المسار</th><th>الرابط</th><th>الحالة</th></tr>";

foreach ($test_routes as $name => $url) {
    echo "<tr>";
    echo "<td>{$name}</td>";
    echo "<td><a href='{$url}' target='_blank'>{$url}</a></td>";
    
    if (strpos($url, 'POST') !== false) {
        echo "<td><span class='info'>يحتاج اختبار يدوي</span></td>";
    } else {
        // محاولة فحص الرابط
        $headers = @get_headers($url);
        if ($headers && strpos($headers[0], '200') !== false) {
            echo "<td><span class='success'>✅ يعمل</span></td>";
        } elseif ($headers && strpos($headers[0], '302') !== false) {
            echo "<td><span class='warning'>⚠️ إعادة توجيه</span></td>";
        } else {
            echo "<td><span class='error'>❌ لا يعمل</span></td>";
        }
    }
    echo "</tr>";
}
echo "</table>";
echo "</div>";

// 3. فحص الكونترولر
echo "<div class='section'>";
echo "<h2>3. فحص الكونترولر</h2>";

$controller_file = 'app/Http/Controllers/ReceiptOrderController.php';
if (file_exists($controller_file)) {
    echo "<span class='success'>✅ ملف الكونترولر موجود</span><br>";
    
    $controller_content = file_get_contents($controller_file);
    
    // فحص الدوال المطلوبة
    $required_methods = ['index', 'create', 'store', 'show', 'edit', 'update', 'destroy'];
    
    foreach ($required_methods as $method) {
        if (strpos($controller_content, "function {$method}") !== false) {
            echo "<span class='success'>✅ دالة {$method} موجودة</span><br>";
        } else {
            echo "<span class='error'>❌ دالة {$method} مفقودة</span><br>";
        }
    }
} else {
    echo "<span class='error'>❌ ملف الكونترولر غير موجود</span><br>";
}
echo "</div>";

// 4. فحص النموذج
echo "<div class='section'>";
echo "<h2>4. فحص النموذج</h2>";

$model_file = 'app/Models/ReceiptOrder.php';
if (file_exists($model_file)) {
    echo "<span class='success'>✅ ملف النموذج موجود</span><br>";
    
    $model_content = file_get_contents($model_file);
    
    // فحص الخصائص المطلوبة
    if (strpos($model_content, '$fillable') !== false) {
        echo "<span class='success'>✅ خاصية fillable موجودة</span><br>";
    } else {
        echo "<span class='error'>❌ خاصية fillable مفقودة</span><br>";
    }
    
    if (strpos($model_content, '$table') !== false) {
        echo "<span class='success'>✅ خاصية table موجودة</span><br>";
    } else {
        echo "<span class='warning'>⚠️ خاصية table غير محددة (سيستخدم الاسم الافتراضي)</span><br>";
    }
} else {
    echo "<span class='error'>❌ ملف النموذج غير موجود</span><br>";
}
echo "</div>";

// 5. فحص صفحة الإنشاء
echo "<div class='section'>";
echo "<h2>5. فحص صفحة الإنشاء</h2>";

$create_view = 'resources/views/receipt_order/create.blade.php';
if (file_exists($create_view)) {
    echo "<span class='success'>✅ صفحة الإنشاء موجودة</span><br>";
    
    $create_content = file_get_contents($create_view);
    
    // فحص العناصر المطلوبة
    $required_elements = [
        'form' => '<form',
        'csrf' => '@csrf',
        'action' => 'action.*receipt-order.store',
        'method' => 'method.*POST',
        'submit' => 'type.*submit'
    ];
    
    foreach ($required_elements as $element => $pattern) {
        if (preg_match('/' . $pattern . '/i', $create_content)) {
            echo "<span class='success'>✅ عنصر {$element} موجود</span><br>";
        } else {
            echo "<span class='error'>❌ عنصر {$element} مفقود</span><br>";
        }
    }
} else {
    echo "<span class='error'>❌ صفحة الإنشاء غير موجودة</span><br>";
}
echo "</div>";

// 6. نصائح الإصلاح
echo "<div class='section'>";
echo "<h2>6. نصائح الإصلاح</h2>";
echo "<ul>";
echo "<li><strong>إذا كانت المسارات مفقودة:</strong> تأكد من وجود Route::resource في web.php</li>";
echo "<li><strong>إذا كان الكونترولر مفقود:</strong> تأكد من رفع الملف للخادم</li>";
echo "<li><strong>إذا كان النموذج مفقود:</strong> تأكد من رفع ملف ReceiptOrder.php</li>";
echo "<li><strong>إذا كانت صفحة الإنشاء مفقودة:</strong> تأكد من رفع ملف create.blade.php</li>";
echo "<li><strong>إذا كان الحفظ لا يعمل:</strong> فحص سجل الأخطاء في storage/logs/laravel.log</li>";
echo "</ul>";
echo "</div>";

echo "<div class='section'>";
echo "<h2>✅ انتهى فحص المسارات</h2>";
echo "<p>إذا وجدت مشاكل، اتبع النصائح أعلاه أو أرسل لي تفاصيل الأخطاء.</p>";
echo "</div>";
?>
