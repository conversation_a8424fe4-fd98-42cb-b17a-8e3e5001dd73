# 💰 دليل احتساب Current Cash (النقد الحالي)

## 🎯 تعريف Current Cash

**Current Cash** هو المبلغ النقدي الموجود فعلياً لدى الكاشير في الصندوق، ويتم تحديثه مع كل معاملة نقدية تحدث خلال الشفت.

## 📊 المعادلة الأساسية

```
Current Cash = الرصيد الافتتاحي + جميع المقبوضات النقدية - جميع المدفوعات النقدية
```

## 🔄 كيفية التحديث

### 1. 🚀 بداية الشفت
```php
// عند إنشاء شفت جديد
FinancialRecord::create([
    'opening_balance' => 5000.00,  // الرصيد الافتتاحي
    'current_cash' => 0.00,        // يبدأ بصفر
    'shift_id' => $shift->id
]);
```

### 2. 💳 مبيعات POS نقدية
```php
// في FinancialRecordService::updateMainFinancialRecord()
if ($payment_type === 'cash') {
    $current_cash = $current_cash + $total_price;
}

// مثال: فاتورة بـ 150 ريال نقدي
// Current Cash = 0.00 + 150.00 = 150.00
```

### 3. 🔄 مبيعات POS مختلطة (نقد + شبكة)
```php
// في FinancialRecordService::updateSplitFinancialRecord()
$current_cash = $record->current_cash + $cash_amount;

// مثال: فاتورة 200 ريال (100 نقد + 100 شبكة)
// Current Cash = 150.00 + 100.00 = 250.00
```

### 4. 📥 سندات القبض النقدية
```php
// في FinancialRecordService::updateCurrentCashOnReceiptVoucher()
if ($payment_method === 'cash') {
    $currentCash = $openShiftFinancialRecord->current_cash + $payment_amount;
}

// مثال: سند قبض 500 ريال نقدي
// Current Cash = 250.00 + 500.00 = 750.00
```

### 5. 📤 سندات الصرف النقدية
```php
// في FinancialRecordService (Payment Voucher)
if ($payment_method === 'cash') {
    $currentCash = $openShiftFinancialRecord->current_cash - $payment_amount;
}

// مثال: سند صرف 200 ريال نقدي
// Current Cash = 750.00 - 200.00 = 550.00
```

## 📋 أنواع المعاملات المؤثرة

### ✅ معاملات تزيد Current Cash:
1. **مبيعات POS نقدية** (`payment_type = 'cash'`)
2. **الجزء النقدي من المبيعات المختلطة** (`cash_amount`)
3. **سندات القبض النقدية** (`payment_method = 'cash'`)

### ❌ معاملات تقلل Current Cash:
1. **سندات الصرف النقدية** (`payment_method = 'cash'`)
2. **المرتجعات النقدية** (إن وجدت)

### 🚫 معاملات لا تؤثر على Current Cash:
1. **مبيعات POS عبر الشبكة** (`payment_type = 'network'`)
2. **سندات القبض/الصرف البنكية** (`payment_method = 'bank_transfer'`)
3. **نقد التوصيل** (`delivery_cash`)

## 🔍 مثال تطبيقي شامل

### بداية الشفت:
```
Opening Balance: 5,000.00 ريال
Current Cash: 0.00 ريال
```

### المعاملات خلال اليوم:

#### 1. فاتورة POS نقدية - 150 ريال
```php
$current_cash = 0.00 + 150.00 = 150.00
```

#### 2. فاتورة POS مختلطة - 200 ريال (120 نقد + 80 شبكة)
```php
$current_cash = 150.00 + 120.00 = 270.00
```

#### 3. سند قبض نقدي - 500 ريال
```php
$current_cash = 270.00 + 500.00 = 770.00
```

#### 4. فاتورة POS عبر الشبكة - 300 ريال
```php
// لا تؤثر على Current Cash
$current_cash = 770.00 (بدون تغيير)
```

#### 5. سند صرف نقدي - 200 ريال
```php
$current_cash = 770.00 - 200.00 = 570.00
```

### النتيجة النهائية:
```
Opening Balance: 5,000.00 ريال
Current Cash: 570.00 ريال
Total Cash: 5,000.00 + 570.00 + delivery_cash = 5,570.00 + delivery_cash
```

## 🏗️ الكود المسؤول عن التحديث

### 1. مبيعات POS العادية:
```php
// app/Services/FinancialRecordService.php - updateMainFinancialRecord()
if ($payment_type === 'cash') {
    $current_cash = $current_cash + $total_price;
}
```

### 2. مبيعات POS المختلطة:
```php
// app/Services/FinancialRecordService.php - updateSplitFinancialRecord()
$current_cash = $record->current_cash + $cash_amount;
```

### 3. سندات القبض:
```php
// app/Services/FinancialRecordService.php - updateCurrentCashOnReceiptVoucher()
if ($payment_method === 'cash') {
    $currentCash = $openShiftFinancialRecord->current_cash + $payment_amount;
}
```

### 4. سندات الصرف:
```php
// app/Services/FinancialRecordService.php (Payment Voucher method)
if ($payment_method === 'cash') {
    $currentCash = $openShiftFinancialRecord->current_cash - $payment_amount;
}
```

## 📊 العلاقة مع الحقول الأخرى

### Current Cash vs Other Cash Types:

```php
// في جدول financial_records
opening_balance    // الرصيد الافتتاحي (ثابت)
current_cash      // النقد الحالي (متغير)
overnetwork_cash  // النقد عبر الشبكة
delivery_cash     // النقد لدى التوصيل
total_cash        // الإجمالي
```

### معادلة Total Cash:
```php
$total_cash = $opening_balance + $current_cash + $delivery_cash;
// ملاحظة: overnetwork_cash لا يُضاف لأنه ليس نقد فعلي
```

## ⚠️ نقاط مهمة

### 1. الفرق بين Current Cash و Delivery Cash:
- **Current Cash**: النقد الموجود لدى الكاشير في الصندوق
- **Delivery Cash**: النقد الموجود لدى موظفي التوصيل

### 2. معالجة التوصيل:
```php
// عند طلب توصيل نقدي
$currentCash = $openShiftFinancialRecord->current_cash; // لا يتغير
$deliveryCash = $openShiftFinancialRecord->delivery_cash + $total_price; // يزيد
$deficit = $openShiftFinancialRecord->deficit + $total_price; // يزيد العجز
```

### 3. معالجة سندات القبض من التوصيل:
```php
// عند استلام نقد من موظف التوصيل
$currentCash = $openShiftFinancialRecord->current_cash + $payment_amount; // يزيد
$deliveryCash = $deliveryCash - $payment_amount; // يقل
```

## 🔧 التحقق من صحة Current Cash

### طريقة التحقق:
```sql
-- احتساب Current Cash المتوقع
SELECT 
    fr.opening_balance,
    fr.current_cash,
    -- مجموع المبيعات النقدية
    COALESCE(SUM(pp.cash_amount), 0) as pos_cash_total,
    -- مجموع سندات القبض النقدية
    COALESCE(SUM(rv.payment_amount), 0) as receipt_cash_total,
    -- مجموع سندات الصرف النقدية  
    COALESCE(SUM(pv.payment_amount), 0) as payment_cash_total,
    -- Current Cash المحسوب
    (COALESCE(SUM(pp.cash_amount), 0) + 
     COALESCE(SUM(rv.payment_amount), 0) - 
     COALESCE(SUM(pv.payment_amount), 0)) as calculated_current_cash
FROM financial_records fr
LEFT JOIN shifts s ON fr.shift_id = s.id
LEFT JOIN pos p ON s.id = p.shift_id
LEFT JOIN pos_payments pp ON p.id = pp.pos_id AND pp.payment_type IN ('cash', 'split')
LEFT JOIN voucher_receipts rv ON s.id = rv.shift_id AND rv.payment_method = 'cash'
LEFT JOIN voucher_payments pv ON s.id = pv.shift_id AND pv.payment_method = 'cash'
WHERE s.id = ?
GROUP BY fr.id;
```

## 📈 أهمية Current Cash

### 1. مراقبة النقد:
- معرفة المبلغ الفعلي في الصندوق
- مقارنة النقد الفعلي مع المحسوب
- اكتشاف العجز أو الزيادة

### 2. إدارة المخاطر:
- تجنب تراكم مبالغ كبيرة
- ضمان توفر النقد للصرف
- مراقبة التدفق النقدي

### 3. التقارير المالية:
- حساب صافي النقد اليومي
- تتبع الأداء المالي
- إعداد تقارير الإدارة

هذا النظام يضمن دقة تتبع النقد الحالي ويوفر رؤية واضحة للوضع المالي في كل لحظة.
