# دليل نشر شاشة مخزون الفرع

## 📋 نظرة عامة

تم إنشاء شاشة جديدة تسمى **"مخزون الفرع"** تحت قسم **نظام نقاط البيع (POS System)** لعرض جميع عمليات المخزون في الفرع.

## 🎯 الوظائف المتاحة

### الجدول الرئيسي يعرض:
- **رقم الأمر**: رقم العملية (نقل أو استلام)
- **المستودع**: اسم المستودع المستهدف
- **عدد المنتجات**: عدد المنتجات في العملية
- **اسم المستخدم**: المستخدم الذي قام بالعملية
- **تاريخ الإنشاء**: تاريخ العملية
- **الحالة**: نوع العملية (استلام بضاعة أو نقل بضاعة)

### الميزات الإضافية:
- **فلترة بالمستودع**: إمكانية فلترة العمليات حسب المستودع
- **عرض التفاصيل**: زر لعرض تفاصيل كل عملية في نافذة منبثقة
- **تصميم متجاوب**: يعمل على جميع الأجهزة

## 📁 الملفات المطلوب نشرها

### 1. الكونترولر الجديد
```
app/Http/Controllers/BranchInventoryController.php
```

### 2. ملف العرض الجديد
```
resources/views/branch_inventory/index.blade.php
```

### 3. الملفات المحدثة
```
routes/web.php
resources/views/partials/admin/menu.blade.php
app/Models/Purchase.php
```

## 🚀 خطوات النشر

### الخطوة 1: رفع الملفات الجديدة
```bash
# رفع الكونترولر الجديد
scp app/Http/Controllers/BranchInventoryController.php user@server:/path/to/project/app/Http/Controllers/

# إنشاء مجلد العرض ورفع الملف
ssh user@server "mkdir -p /path/to/project/resources/views/branch_inventory"
scp resources/views/branch_inventory/index.blade.php user@server:/path/to/project/resources/views/branch_inventory/
```

### الخطوة 2: تحديث الملفات الموجودة
```bash
# تحديث المسارات
scp routes/web.php user@server:/path/to/project/routes/

# تحديث القائمة الجانبية
scp resources/views/partials/admin/menu.blade.php user@server:/path/to/project/resources/views/partials/admin/

# تحديث نموذج Purchase
scp app/Models/Purchase.php user@server:/path/to/project/app/Models/
```

### الخطوة 3: تحديث الصلاحيات (إذا لزم الأمر)
```bash
# تحديث صلاحيات الملفات
ssh user@server "chmod 644 /path/to/project/app/Http/Controllers/BranchInventoryController.php"
ssh user@server "chmod 644 /path/to/project/resources/views/branch_inventory/index.blade.php"
```

### الخطوة 4: مسح الكاش
```bash
# مسح كاش Laravel
ssh user@server "cd /path/to/project && php artisan cache:clear"
ssh user@server "cd /path/to/project && php artisan config:clear"
ssh user@server "cd /path/to/project && php artisan route:clear"
ssh user@server "cd /path/to/project && php artisan view:clear"
```

## 🔐 الصلاحيات المطلوبة

الصفحة تتطلب إحدى الصلاحيات التالية:
- `manage warehouse`
- `show warehouse`

## 🌐 الوصول للصفحة

بعد النشر، يمكن الوصول للصفحة من:
- **القائمة الجانبية**: POS System → مخزون الفرع
- **الرابط المباشر**: `/branch-inventory`

## 📊 مصادر البيانات

الصفحة تجمع البيانات من:
1. **جدول warehouse_transfers**: عمليات نقل البضائع
2. **جدول purchases**: عمليات استلام البضائع
3. **جدول purchase_products**: تفاصيل منتجات المشتريات

## ✅ اختبار الوظائف

بعد النشر، تأكد من:
- [ ] ظهور الرابط في القائمة الجانبية
- [ ] تحميل الصفحة بدون أخطاء
- [ ] عرض البيانات بشكل صحيح
- [ ] عمل الفلترة بالمستودع
- [ ] عمل زر عرض التفاصيل
- [ ] عرض النافذة المنبثقة للتفاصيل

## 🔧 استكشاف الأخطاء

### إذا لم تظهر الصفحة:
1. تحقق من الصلاحيات
2. امسح الكاش
3. تحقق من ملف المسارات

### إذا لم تظهر البيانات:
1. تحقق من وجود بيانات في الجداول
2. تحقق من صلاحيات قاعدة البيانات
3. راجع ملف الكونترولر

## 📝 ملاحظات مهمة

- الصفحة تعرض فقط العمليات الخاصة بالمستخدم الحالي
- البيانات مرتبة حسب تاريخ الإنشاء (الأحدث أولاً)
- النظام يدعم عرض التفاصيل عبر AJAX
- التصميم متوافق مع باقي صفحات النظام

## 🎉 انتهاء النشر

بعد اتباع جميع الخطوات، ستكون شاشة مخزون الفرع جاهزة للاستخدام!
