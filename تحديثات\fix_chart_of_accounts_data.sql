-- إصلاح بيانات شجرة الحسابات
-- تشغيل هذا الملف إذا كانت البيانات مفقودة

-- 1. إدراج أنواع الحسابات الأساسية
INSERT IGNORE INTO `chart_of_account_types` (`id`, `name`, `created_by`, `created_at`, `updated_at`) VALUES
(1, 'Assets', 1, NOW(), NOW()),
(2, 'Liabilities', 1, NOW(), NOW()),
(3, 'Equity', 1, NOW(), NOW()),
(4, 'Income', 1, NOW(), NOW()),
(5, 'Expenses', 1, NOW(), NOW()),
(6, 'Costs of Goods Sold', 1, NOW(), NOW());

-- 2. إدراج الأنواع الفرعية للحسابات
INSERT IGNORE INTO `chart_of_account_sub_types` (`id`, `name`, `type`, `created_by`, `created_at`, `updated_at`) VALUES
-- Assets subtypes
(1, 'Current Asset', 1, 1, NOW(), NOW()),
(2, 'Inventory Asset', 1, 1, NOW(), NOW()),
(3, 'Non-current Asset', 1, 1, NOW(), NOW()),

-- Liabilities subtypes
(4, 'Current Liabilities', 2, 1, NOW(), NOW()),
(5, 'Long Term Liabilities', 2, 1, NOW(), NOW()),
(6, 'Share Capital', 2, 1, NOW(), NOW()),
(7, 'Retained Earnings', 2, 1, NOW(), NOW()),

-- Equity subtypes
(8, 'Owners Equity', 3, 1, NOW(), NOW()),

-- Income subtypes
(9, 'Sales Revenue', 4, 1, NOW(), NOW()),
(10, 'Other Revenue', 4, 1, NOW(), NOW()),

-- Costs of Goods Sold subtypes
(11, 'Costs of Goods Sold', 6, 1, NOW(), NOW()),

-- Expenses subtypes
(12, 'Payroll Expenses', 5, 1, NOW(), NOW()),
(13, 'General and Administrative expenses', 5, 1, NOW(), NOW());

-- 3. إدراج الحسابات الأساسية
INSERT IGNORE INTO `chart_of_accounts` (`id`, `name`, `code`, `type`, `sub_type`, `parent`, `is_enabled`, `description`, `created_by`, `created_at`, `updated_at`) VALUES
-- Assets
(1, 'Checking Account', 1060, 1, 1, 0, 1, 'Main checking account', 1, NOW(), NOW()),
(2, 'Petty Cash', 1065, 1, 1, 0, 1, 'Petty cash account', 1, NOW(), NOW()),
(3, 'Account Receivables', 1200, 1, 1, 0, 1, 'Customer receivables', 1, NOW(), NOW()),
(4, 'Inventory', 1510, 1, 2, 0, 1, 'Main inventory account', 1, NOW(), NOW()),

-- Liabilities
(5, 'Accounts Payable', 2000, 2, 4, 0, 1, 'Vendor payables', 1, NOW(), NOW()),
(6, 'Accrued Liabilities', 2100, 2, 4, 0, 1, 'Accrued expenses', 1, NOW(), NOW()),

-- Equity
(7, 'Owner Equity', 3000, 3, 8, 0, 1, 'Owner equity account', 1, NOW(), NOW()),
(8, 'Retained Earnings', 3200, 3, 8, 0, 1, 'Retained earnings', 1, NOW(), NOW()),

-- Income
(9, 'Sales Revenue', 4000, 4, 9, 0, 1, 'Main sales revenue', 1, NOW(), NOW()),
(10, 'Service Revenue', 4100, 4, 9, 0, 1, 'Service income', 1, NOW(), NOW()),
(11, 'Other Income', 4200, 4, 10, 0, 1, 'Miscellaneous income', 1, NOW(), NOW()),

-- Expenses
(12, 'Office Expenses', 5000, 5, 13, 0, 1, 'General office expenses', 1, NOW(), NOW()),
(13, 'Rent Expense', 5100, 5, 13, 0, 1, 'Office rent', 1, NOW(), NOW()),
(14, 'Utilities Expense', 5200, 5, 13, 0, 1, 'Utilities and services', 1, NOW(), NOW()),
(15, 'Salary Expense', 5300, 5, 12, 0, 1, 'Employee salaries', 1, NOW(), NOW()),

-- Cost of Goods Sold
(16, 'Cost of Goods Sold', 6000, 6, 11, 0, 1, 'Direct costs of products sold', 1, NOW(), NOW());

-- 4. تحديث البيانات للمستخدمين الآخرين (إذا لزم الأمر)
-- يمكنك تغيير created_by = 1 إلى معرف المستخدم المناسب

-- 5. فحص البيانات المدرجة
SELECT 'Chart of Account Types' as 'Table', COUNT(*) as 'Count' FROM chart_of_account_types
UNION ALL
SELECT 'Chart of Account Sub Types', COUNT(*) FROM chart_of_account_sub_types
UNION ALL
SELECT 'Chart of Accounts', COUNT(*) FROM chart_of_accounts;

-- 6. عرض البيانات المدرجة
SELECT 'Types:' as info;
SELECT id, name, created_by FROM chart_of_account_types;

SELECT 'Sub Types:' as info;
SELECT id, name, type, created_by FROM chart_of_account_sub_types;

SELECT 'Accounts:' as info;
SELECT id, code, name, type, sub_type, is_enabled, created_by FROM chart_of_accounts;
