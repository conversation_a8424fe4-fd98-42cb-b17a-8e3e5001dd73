<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تشخيص إدارة النقد المتقدمة</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>
    <div class="container mt-5">
        <h2>تشخيص إدارة النقد المتقدمة</h2>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5>اختبار API Endpoints</h5>
                    </div>
                    <div class="card-body">
                        <button class="btn btn-primary me-2" onclick="testQuickStats()">اختبار الإحصائيات السريعة</button>
                        <button class="btn btn-primary me-2" onclick="testShiftsData()">اختبار بيانات الشفتات</button>
                        <button class="btn btn-primary me-2" onclick="testReceiptVouchers()">اختبار سندات القبض</button>
                        <button class="btn btn-primary me-2" onclick="testPaymentVouchers()">اختبار سندات الصرف</button>
                        <button class="btn btn-primary me-2" onclick="testPOSData()">اختبار بيانات POS</button>
                        <button class="btn btn-primary me-2" onclick="testAlerts()">اختبار التنبيهات</button>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5>نتائج الاختبار</h5>
                    </div>
                    <div class="card-body">
                        <div id="results" style="height: 400px; overflow-y: auto; background: #f8f9fa; padding: 15px; border-radius: 5px;">
                            <p>انقر على الأزرار أعلاه لاختبار API endpoints...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function logResult(title, data, isError = false) {
            const results = document.getElementById('results');
            const timestamp = new Date().toLocaleTimeString();
            const color = isError ? 'text-danger' : 'text-success';
            
            results.innerHTML += `
                <div class="mb-3">
                    <h6 class="${color}">[${timestamp}] ${title}</h6>
                    <pre style="background: white; padding: 10px; border-radius: 3px; font-size: 12px;">${JSON.stringify(data, null, 2)}</pre>
                </div>
            `;
            results.scrollTop = results.scrollHeight;
        }

        function testQuickStats() {
            $.ajax({
                url: '/financial-operations/api/quick-stats',
                method: 'GET',
                data: {
                    start_date: '2024-01-01',
                    end_date: '2024-12-31'
                },
                success: function(response) {
                    logResult('✅ Quick Stats - نجح', response);
                },
                error: function(xhr) {
                    logResult('❌ Quick Stats - فشل', {
                        status: xhr.status,
                        statusText: xhr.statusText,
                        responseText: xhr.responseText
                    }, true);
                }
            });
        }

        function testShiftsData() {
            $.ajax({
                url: '/financial-operations/api/shifts-data',
                method: 'GET',
                data: {
                    start_date: '2024-01-01',
                    end_date: '2024-12-31'
                },
                success: function(response) {
                    logResult('✅ Shifts Data - نجح', response);
                },
                error: function(xhr) {
                    logResult('❌ Shifts Data - فشل', {
                        status: xhr.status,
                        statusText: xhr.statusText,
                        responseText: xhr.responseText
                    }, true);
                }
            });
        }

        function testReceiptVouchers() {
            $.ajax({
                url: '/financial-operations/api/receipt-vouchers',
                method: 'GET',
                data: {
                    start_date: '2024-01-01',
                    end_date: '2024-12-31'
                },
                success: function(response) {
                    logResult('✅ Receipt Vouchers - نجح', response);
                },
                error: function(xhr) {
                    logResult('❌ Receipt Vouchers - فشل', {
                        status: xhr.status,
                        statusText: xhr.statusText,
                        responseText: xhr.responseText
                    }, true);
                }
            });
        }

        function testPaymentVouchers() {
            $.ajax({
                url: '/financial-operations/api/payment-vouchers',
                method: 'GET',
                data: {
                    start_date: '2024-01-01',
                    end_date: '2024-12-31'
                },
                success: function(response) {
                    logResult('✅ Payment Vouchers - نجح', response);
                },
                error: function(xhr) {
                    logResult('❌ Payment Vouchers - فشل', {
                        status: xhr.status,
                        statusText: xhr.statusText,
                        responseText: xhr.responseText
                    }, true);
                }
            });
        }

        function testPOSData() {
            $.ajax({
                url: '/financial-operations/api/pos-sales',
                method: 'GET',
                data: {
                    start_date: '2024-01-01',
                    end_date: '2024-12-31'
                },
                success: function(response) {
                    logResult('✅ POS Data - نجح', response);
                },
                error: function(xhr) {
                    logResult('❌ POS Data - فشل', {
                        status: xhr.status,
                        statusText: xhr.statusText,
                        responseText: xhr.responseText
                    }, true);
                }
            });
        }

        function testAlerts() {
            $.ajax({
                url: '/financial-operations/api/alerts',
                method: 'GET',
                success: function(response) {
                    logResult('✅ Alerts - نجح', response);
                },
                error: function(xhr) {
                    logResult('❌ Alerts - فشل', {
                        status: xhr.status,
                        statusText: xhr.statusText,
                        responseText: xhr.responseText
                    }, true);
                }
            });
        }

        // Test all endpoints on page load
        $(document).ready(function() {
            logResult('🚀 بدء اختبار جميع API endpoints...', {});
            
            setTimeout(() => testQuickStats(), 500);
            setTimeout(() => testShiftsData(), 1000);
            setTimeout(() => testReceiptVouchers(), 1500);
            setTimeout(() => testPaymentVouchers(), 2000);
            setTimeout(() => testPOSData(), 2500);
            setTimeout(() => testAlerts(), 3000);
        });
    </script>
</body>
</html>
