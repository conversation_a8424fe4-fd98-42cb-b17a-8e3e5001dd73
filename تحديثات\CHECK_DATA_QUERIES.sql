-- 🔍 استعلامات للتحقق من البيانات في نظام تحليل المبيعات

-- 1. التحقق من وجود فواتير في جدول pos
SELECT COUNT(*) as total_invoices FROM pos;

-- 2. التحقق من فواتير اليوم
SELECT COUNT(*) as today_invoices 
FROM pos 
WHERE DATE(pos_date) = CURDATE();

-- 3. التحقق من المدفوعات
SELECT COUNT(*) as total_payments FROM pos_payments;

-- 4. التحقق من فواتير المستخدم الحالي (غير معرف المستخدم بـ 2)
SELECT COUNT(*) as user_invoices 
FROM pos 
WHERE created_by = 2;

-- 5. التحقق من فواتير اليوم للمستخدم الحالي
SELECT COUNT(*) as user_today_invoices 
FROM pos 
WHERE created_by = 2 
AND DATE(pos_date) = CURDATE();

-- 6. التحقق من إجمالي المبالغ لليوم
SELECT 
    COUNT(p.id) as invoice_count,
    SUM(pp.amount) as total_amount
FROM pos p
LEFT JOIN pos_payments pp ON p.id = pp.pos_id
WHERE p.created_by = 2 
AND DATE(p.pos_date) = CURDATE();

-- 7. التحقق من آخر 10 فواتير
SELECT 
    p.id,
    p.pos_date,
    p.created_by,
    pp.amount,
    c.name as customer_name
FROM pos p
LEFT JOIN pos_payments pp ON p.id = pp.pos_id
LEFT JOIN customers c ON p.customer_id = c.id
WHERE p.created_by = 2
ORDER BY p.created_at DESC
LIMIT 10;

-- 8. التحقق من المستودعات المتاحة
SELECT id, name FROM warehouses WHERE created_by = 2;

-- 9. التحقق من فواتير هذا الشهر
SELECT 
    COUNT(p.id) as monthly_invoices,
    SUM(pp.amount) as monthly_amount
FROM pos p
LEFT JOIN pos_payments pp ON p.id = pp.pos_id
WHERE p.created_by = 2 
AND YEAR(p.pos_date) = YEAR(CURDATE())
AND MONTH(p.pos_date) = MONTH(CURDATE());

-- 10. التحقق من فواتير هذا الأسبوع
SELECT 
    COUNT(p.id) as weekly_invoices,
    SUM(pp.amount) as weekly_amount
FROM pos p
LEFT JOIN pos_payments pp ON p.id = pp.pos_id
WHERE p.created_by = 2 
AND YEARWEEK(p.pos_date) = YEARWEEK(CURDATE());
