<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار حساب قيمة البيع</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            direction: rtl;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select {
            width: 300px;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .result {
            background-color: #f0f8ff;
            padding: 10px;
            border-radius: 4px;
            margin-top: 20px;
        }
        .example {
            background-color: #f9f9f9;
            padding: 15px;
            border-radius: 4px;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <h1>اختبار حساب استخراج ضريبة القيمة المضافة</h1>

    <div class="form-group">
        <label for="sale_value_helper">المبلغ الإجمالي (شامل ضريبة القيمة المضافة):</label>
        <input type="number" id="sale_value_helper" step="0.01" placeholder="أدخل المبلغ الإجمالي شامل الضريبة">
    </div>

    <div class="form-group">
        <label for="tax_rate">معدل الضريبة (%):</label>
        <select id="tax_rate">
            <option value="0">بدون ضريبة</option>
            <option value="5">5% ضريبة</option>
            <option value="15" selected>15% ضريبة القيمة المضافة</option>
            <option value="20">20% ضريبة</option>
        </select>
    </div>

    <div class="form-group">
        <label for="sale_price">سعر البيع (قبل الضريبة):</label>
        <input type="number" id="sale_price" step="0.01" readonly style="background-color: #f5f5f5;">
    </div>

    <div class="result" id="calculation_result">
        <h3>نتيجة الحساب:</h3>
        <p id="result_text">أدخل قيمة البيع لرؤية النتيجة</p>
    </div>

    <div class="example">
        <h3>مثال:</h3>
        <p><strong>إذا كان المبلغ الإجمالي شامل الضريبة = 115 ريال</strong></p>
        <p><strong>ومعدل ضريبة القيمة المضافة = 15%</strong></p>
        <p><strong>فإن سعر البيع قبل الضريبة = 115 ÷ (1 + 15%) = 115 ÷ 1.15 = 100 ريال</strong></p>
        <p><strong>ومبلغ الضريبة = 115 - 100 = 15 ريال</strong></p>
    </div>

    <script>
        function calculateSalePrice() {
            const saleValueHelper = parseFloat(document.getElementById('sale_value_helper').value) || 0;
            const taxRate = parseFloat(document.getElementById('tax_rate').value) || 0;

            if (saleValueHelper <= 0) {
                document.getElementById('sale_price').value = '';
                document.getElementById('result_text').textContent = 'أدخل المبلغ الإجمالي لرؤية النتيجة';
                return;
            }

            // حساب سعر البيع قبل الضريبة: المبلغ الإجمالي ÷ (1 + معدل الضريبة / 100)
            const salePriceBeforeVAT = saleValueHelper / (1 + (taxRate / 100));
            const taxAmount = saleValueHelper - salePriceBeforeVAT;

            document.getElementById('sale_price').value = salePriceBeforeVAT.toFixed(2);

            // عرض تفاصيل الحساب
            const resultText = `
                المبلغ الإجمالي شامل الضريبة: ${saleValueHelper.toFixed(2)} ريال
                معدل الضريبة: ${taxRate}%
                مبلغ الضريبة المستخرج: ${taxAmount.toFixed(2)} ريال
                سعر البيع قبل الضريبة: ${salePriceBeforeVAT.toFixed(2)} ريال
            `;
            document.getElementById('result_text').innerHTML = resultText.replace(/\n/g, '<br>');
        }

        // ربط الأحداث
        document.getElementById('sale_value_helper').addEventListener('input', calculateSalePrice);
        document.getElementById('tax_rate').addEventListener('change', calculateSalePrice);

        // حساب أولي
        calculateSalePrice();
    </script>
</body>
</html>
