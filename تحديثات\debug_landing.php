<?php
// تحميل Laravel
require_once 'vendor/autoload.php';
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Models\Utility;
use Illuminate\Support\Facades\Schema;

echo "<h1>🔍 تشخيص مشكلة صفحة الهبوط</h1>";
echo "<hr>";

// 1. فحص ملف التثبيت
echo "<h2>📁 فحص ملف التثبيت:</h2>";
$installedFile = storage_path() . "/installed";
if (file_exists($installedFile)) {
    echo "<p style='color: green;'>✅ ملف التثبيت موجود: $installedFile</p>";
    echo "<p>محتوى الملف: " . file_get_contents($installedFile) . "</p>";
} else {
    echo "<p style='color: red;'>❌ ملف التثبيت غير موجود: $installedFile</p>";
}

// 2. فحص إعدادات النظام
echo "<h2>⚙️ فحص إعدادات النظام:</h2>";
try {
    $adminSettings = Utility::settings();
    echo "<p style='color: green;'>✅ تم تحميل إعدادات النظام بنجاح</p>";
    
    // فحص إعداد صفحة الهبوط
    $displayLanding = $adminSettings['display_landing_page'] ?? 'غير محدد';
    echo "<p><strong>display_landing_page:</strong> '$displayLanding'</p>";
    
    if ($displayLanding == 'on') {
        echo "<p style='color: green;'>✅ صفحة الهبوط مفعلة</p>";
    } else {
        echo "<p style='color: orange;'>⚠️ صفحة الهبوط غير مفعلة - سيتم التوجيه لصفحة تسجيل الدخول</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ في تحميل إعدادات النظام: " . $e->getMessage() . "</p>";
}

// 3. فحص جدول landing_page_settings
echo "<h2>🗄️ فحص جدول landing_page_settings:</h2>";
try {
    if (Schema::hasTable('landing_page_settings')) {
        echo "<p style='color: green;'>✅ جدول landing_page_settings موجود</p>";
        
        // عد السجلات
        $count = DB::table('landing_page_settings')->count();
        echo "<p>عدد السجلات في الجدول: $count</p>";
        
    } else {
        echo "<p style='color: red;'>❌ جدول landing_page_settings غير موجود</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ في فحص الجدول: " . $e->getMessage() . "</p>";
}

// 4. فحص الشرط الكامل
echo "<h2>🔍 فحص الشرط الكامل:</h2>";
try {
    $adminSettings = Utility::settings();
    $hasTable = Schema::hasTable('landing_page_settings');
    $displayLanding = $adminSettings['display_landing_page'] ?? '';
    
    echo "<p><strong>الشرط:</strong> display_landing_page == 'on' AND landing_page_settings table exists</p>";
    echo "<p><strong>display_landing_page:</strong> '$displayLanding' (" . ($displayLanding == 'on' ? 'TRUE' : 'FALSE') . ")</p>";
    echo "<p><strong>landing_page_settings exists:</strong> " . ($hasTable ? 'TRUE' : 'FALSE') . "</p>";
    
    $condition = ($displayLanding == 'on' && $hasTable);
    echo "<p><strong>النتيجة النهائية:</strong> " . ($condition ? 'TRUE - سيتم عرض صفحة الهبوط' : 'FALSE - سيتم التوجيه لصفحة تسجيل الدخول') . "</p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ في فحص الشرط: " . $e->getMessage() . "</p>";
}

// 5. اقتراحات الحل
echo "<h2>💡 اقتراحات الحل:</h2>";
echo "<ol>";
echo "<li>إذا كانت صفحة الهبوط غير مفعلة، قم بتفعيلها من إعدادات النظام</li>";
echo "<li>إذا كان جدول landing_page_settings غير موجود، قم بتشغيل migrations</li>";
echo "<li>أو يمكنك الوصول مباشرة لصفحة تسجيل الدخول: <a href='http://localhost/up20251/login'>http://localhost/up20251/login</a></li>";
echo "</ol>";

echo "<hr>";
echo "<p><strong>🌐 روابط مفيدة:</strong></p>";
echo "<ul>";
echo "<li><a href='http://localhost/up20251/login'>صفحة تسجيل الدخول</a></li>";
echo "<li><a href='http://localhost/up20251/register'>صفحة التسجيل</a></li>";
echo "<li><a href='http://localhost/up20251/dashboard'>لوحة التحكم</a></li>";
echo "</ul>";
?>
