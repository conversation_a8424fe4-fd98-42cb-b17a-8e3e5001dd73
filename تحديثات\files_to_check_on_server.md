# قائمة الملفات المرتبطة بصلاحية is_sale_session_new للفحص في السيرفر

## 🎮 1. Controllers (المتحكمات)

### الملف الأساسي - مطلوب بشدة
```
app/Http/Controllers/FinancialRecordController.php
```
**ما تبحث عنه:**
- Method: `index()`
- Method: `opinningBalace()`
- Method: `SetOpeningBalance()`
- Method: `closeShift()`
- استخدام: `Auth::user()['is_sale_session_new']`

### ملف مساعد - مطلوب للإدارة
```
app/Http/Controllers/BranchCashManagementController.php
```
**ما تبحث عنه:**
- Method: `closeShift()`
- Method: `reopenShift()`
- تحديث: `$user->is_sale_session_new = true/false`

## 🏗️ 2. Models (النماذج)

### الملف الأساسي - مطلوب بشدة
```
app/Models/User.php
```
**ما تبحث عنه:**
- في array `$fillable`: يجب أن يحتوي على `'warehouse_id'`
- تأكد من عدم وجود `'is_sale_session_new'` في `$hidden`

### ملفات مساعدة - مطلوبة
```
app/Models/Shift.php
app/Models/FinancialRecord.php
app/Models/DeliveryFinancialRecord.php
app/Models/FinancialTransactions.php
```

## 🔧 3. Services (الخدمات)

```
app/Services/FinancialRecordService.php
app/Services/FinancialTransactionService.php
```

## 🎨 4. Views (العروض)

### الملف الأساسي - مطلوب بشدة
```
resources/views/pos/financial_record/opening-balance.blade.php
```
**ما تبحث عنه:**
- Form مع action: `route('pos.financial.record.opening.balance')`
- Input field: `name="opening_balance"`

### ملفات مساعدة
```
resources/views/pos/financial_record/index.blade.php
resources/views/pos/financial_record/index_delivery.blade.php
```

### الملف الحيوي - مطلوب بشدة
```
resources/views/partials/admin/menu.blade.php
```
**ما تبحث عنه:**
- السطر: `$is_sale_session_new = Auth::user()['is_sale_session_new'] && Auth::user()->can('manage pos');`
- في قسم POS: `data-fmodel="{{$is_sale_session_new ? 'true' : ''}}"`
- JavaScript في نهاية الملف: `$('a[data-fmodel="true"]').click(function () {`

### ملفات أخرى تستخدم المتغير
```
resources/views/voucher/payment/show.blade.php
resources/views/voucher/receipt/show.blade.php
resources/views/bankAccount/financial_record/index.blade.php
```

## 🛣️ 5. Routes (المسارات)

### الملف الأساسي - مطلوب بشدة
```
routes/web.php
```
**ما تبحث عنه - يجب إضافة هذه الـ routes:**
```php
//for financial record
Route::get('pos-financial-record', [FinancialRecordController::class, 'index'])->name('pos.financial.record')->middleware(['auth', 'XSS']);
Route::post('pos-financial-record', [FinancialRecordController::class, 'SetOpeningBalance'])->name('pos.financial.record.opening.balance')->middleware(['auth', 'XSS']);
Route::post('pos-financial-record/closing-shift', [FinancialRecordController::class, 'closeShift'])->name('pos.financial.record.closing.shift')->middleware(['auth', 'XSS']);
Route::get('pos-financial-record/opening-balance', [FinancialRecordController::class, 'opinningBalace'])->name('pos.financial.opening.balance')->middleware(['auth', 'XSS']);
```

## 🗄️ 6. Database Migrations (هجرة قاعدة البيانات)

### الملف الأساسي - مطلوب بشدة
```
database/migrations/2025_03_09_184355_add_is_sale_session_new_to_users_table.php
```

### ملفات مساعدة
```
database/migrations/2025_03_08_192200_create_shifts_table.php
database/migrations/2025_03_09_131457_create_financial_records_table.php
database/migrations/2025_03_24_152432_create_delivery_financial_records_table.php
database/migrations/2025_03_26_205047_create_financial_transactions_table.php
```

## 📋 7. خطوات الفحص في السيرفر

### الخطوة 1: فحص الملفات الأساسية
```bash
# تحقق من وجود الملفات
ls -la app/Http/Controllers/FinancialRecordController.php
ls -la resources/views/pos/financial_record/opening-balance.blade.php
ls -la resources/views/partials/admin/menu.blade.php
```

### الخطوة 2: فحص محتوى الملفات الحيوية
```bash
# فحص FinancialRecordController
grep -n "opinningBalace" app/Http/Controllers/FinancialRecordController.php
grep -n "is_sale_session_new" app/Http/Controllers/FinancialRecordController.php

# فحص menu.blade.php
grep -n "is_sale_session_new" resources/views/partials/admin/menu.blade.php
grep -n "data-fmodel" resources/views/partials/admin/menu.blade.php
```

### الخطوة 3: فحص Routes
```bash
# فحص routes
grep -n "pos.financial.opening.balance" routes/web.php
grep -n "FinancialRecordController" routes/web.php
```

### الخطوة 4: فحص قاعدة البيانات
```sql
-- فحص وجود الحقل
DESCRIBE users;

-- فحص الجداول المطلوبة
SHOW TABLES LIKE 'shifts';
SHOW TABLES LIKE 'financial_records';

-- فحص بيانات المستخدم
SELECT id, name, warehouse_id, is_sale_session_new FROM users WHERE id = [USER_ID];
```

### الخطوة 5: فحص الصلاحيات
```sql
-- فحص صلاحيات المستخدم
SELECT p.name 
FROM permissions p
JOIN model_has_permissions mhp ON p.id = mhp.permission_id
WHERE mhp.model_id = [USER_ID] AND p.name IN ('manage pos', 'show financial record');
```

## 🚨 8. الملفات الأكثر أهمية للفحص (بالأولوية)

### أولوية عالية جداً
1. `app/Http/Controllers/FinancialRecordController.php`
2. `resources/views/pos/financial_record/opening-balance.blade.php`
3. `resources/views/partials/admin/menu.blade.php`
4. `routes/web.php`

### أولوية عالية
5. `app/Models/User.php`
6. Migration: `add_is_sale_session_new_to_users_table.php`

### أولوية متوسطة
7. `app/Services/FinancialRecordService.php`
8. `app/Models/Shift.php`
9. `app/Models/FinancialRecord.php`

## 🔍 9. أوامر التشخيص السريع

### فحص Laravel
```bash
# مسح cache
php artisan config:cache
php artisan route:cache
php artisan view:cache

# فحص routes
php artisan route:list | grep financial

# تشغيل migrations
php artisan migrate:status
php artisan migrate
```

### فحص الأخطاء
```bash
# فحص Laravel logs
tail -f storage/logs/laravel.log

# فحص web server logs
tail -f /var/log/apache2/error.log  # أو nginx
```

## ✅ 10. قائمة فحص سريعة

- [ ] ملف FinancialRecordController موجود ويحتوي على method opinningBalace
- [ ] ملف opening-balance.blade.php موجود في المسار الصحيح
- [ ] ملف menu.blade.php يحتوي على المتغير is_sale_session_new
- [ ] Routes المطلوبة موجودة في web.php
- [ ] حقل is_sale_session_new موجود في جدول users
- [ ] المستخدم لديه صلاحيات manage pos
- [ ] قيمة is_sale_session_new = 1 للمستخدم
- [ ] Cache تم مسحه بعد التحديثات
