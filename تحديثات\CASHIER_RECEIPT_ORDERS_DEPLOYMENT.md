# دليل نشر أوامر الاستلام لدور Cashier

## 🎯 الهدف
إضافة إمكانية الوصول الكامل إلى نظام أوامر الاستلام للمستخدمين الذين لديهم دور **Cashier** في نظام نقاط البيع، بما في ذلك العرض والإنشاء.

## ✅ التغييرات المطلوبة

### 📁 **الملفات المحدثة:**

#### **1. القائمة الجانبية**
```
📁 resources/views/partials/admin/menu.blade.php
```
**التغيير:**
- إضافة عنصر "أوامر الاستلام" في قسم POS للمستخدمين الذين لديهم دور Cashier
- العنصر يظهر بجانب "تاريخ الصلاحية للمنتجات"

#### **2. الكونترولر**
```
📁 app/Http/Controllers/ReceiptOrderController.php
```
**التغييرات:**
- تعديل دالة `index()` للسماح لدور Cashier بالوصول
- تعديل دالة `show()` للسماح لدور Cashier بعرض التفاصيل
- تعديل دالة `create()` للسماح لدور Cashier بإنشاء أوامر جديدة
- تعديل دالة `store()` للسماح لدور Cashier بحفظ أوامر الاستلام

#### **3. ملف العرض**
```
📁 resources/views/receipt_order/index.blade.php
```
**التغيير:**
- تعديل شرط إظهار زر "إنشاء أمر استلام" ليظهر للمستخدمين الذين لديهم دور Cashier

## 🚀 **خطوات النشر**

### **الخطوة 1: رفع الملفات المحدثة**
```bash
# رفع ملف القائمة الجانبية
scp resources/views/partials/admin/menu.blade.php user@server:/path/to/project/resources/views/partials/admin/

# رفع الكونترولر المحدث
scp app/Http/Controllers/ReceiptOrderController.php user@server:/path/to/project/app/Http/Controllers/

# رفع ملف العرض المحدث
scp resources/views/receipt_order/index.blade.php user@server:/path/to/project/resources/views/receipt_order/
```

### **الخطوة 2: ضبط الصلاحيات**
```bash
# ضبط صلاحيات الملفات
ssh user@server "chmod 644 /path/to/project/resources/views/partials/admin/menu.blade.php"
ssh user@server "chmod 644 /path/to/project/app/Http/Controllers/ReceiptOrderController.php"
ssh user@server "chmod 644 /path/to/project/resources/views/receipt_order/index.blade.php"
```

### **الخطوة 3: مسح الكاش**
```bash
# مسح كاش Laravel
ssh user@server "cd /path/to/project && php artisan cache:clear"
ssh user@server "cd /path/to/project && php artisan view:clear"
ssh user@server "cd /path/to/project && php artisan route:clear"
ssh user@server "cd /path/to/project && php artisan config:clear"
```

## 🔐 **الصلاحيات المطلوبة**

### **للمستخدمين الذين لديهم دور Cashier:**
- ✅ **عرض أوامر الاستلام**
- ✅ **عرض تفاصيل أوامر الاستلام**
- ✅ **إنشاء أوامر استلام جديدة**
- ✅ **حفظ أوامر الاستلام**
- ✅ **طباعة أوامر الاستلام**
- ✅ **تحميل PDF لأوامر الاستلام**

## 🌐 **الوصول للنظام**

### **للمستخدمين الذين لديهم دور Cashier:**
1. تسجيل الدخول للنظام
2. الذهاب إلى قسم **نظام نقاط البيع** في القائمة الجانبية
3. النقر على **"أوامر الاستلام"**
4. عرض قائمة أوامر الاستلام المتاحة
5. النقر على زر **"إنشاء أمر استلام"** لإنشاء أمر جديد
6. النقر على أي أمر لعرض التفاصيل
7. استخدام أزرار الطباعة وتحميل PDF

## 🧪 **للاختبار**

### **1. اختبار الوصول:**
```
✅ تسجيل الدخول بحساب لديه دور Cashier
✅ التحقق من ظهور عنصر "أوامر الاستلام" في قائمة POS
✅ النقر على الرابط والتأكد من عمله
✅ التحقق من عرض قائمة أوامر الاستلام
✅ التحقق من ظهور زر "إنشاء أمر استلام"
```

### **2. اختبار الصلاحيات:**
```
✅ التأكد من إمكانية عرض قائمة أوامر الاستلام
✅ التأكد من إمكانية عرض تفاصيل أمر الاستلام
✅ التأكد من إمكانية إنشاء أمر استلام جديد
✅ التأكد من إمكانية حفظ أمر الاستلام
✅ التأكد من إمكانية طباعة أوامر الاستلام
✅ التأكد من إمكانية تحميل PDF
```

## 📋 **ملاحظات مهمة**

1. **صلاحيات كاملة:** المستخدمون الذين لديهم دور Cashier يمكنهم الآن عرض وإنشاء أوامر الاستلام
2. **الأمان:** التحقق من الصلاحيات يتم على مستوى الكونترولر
3. **التوافق:** التغييرات متوافقة مع الأدوار الأخرى الموجودة
4. **الأداء:** لا تؤثر التغييرات على أداء النظام
5. **أنواع الأوامر:** يمكن للمستخدمين إنشاء جميع أنواع أوامر الاستلام (استلام بضاعة، نقل بضاعة، أمر إخراج)

## 🔄 **التراجع عن التغييرات (إذا لزم الأمر)**

### **إزالة الوصول لدور Cashier:**
```bash
# التراجع عن تغييرات القائمة الجانبية
# حذف الأسطر المضافة من menu.blade.php (الأسطر 1410-1416)

# التراجع عن تغييرات الكونترولر
# إزالة "|| Auth::user()->hasRole('Cashier')" من الدوال: index(), show(), create(), store()

# التراجع عن تغييرات ملف العرض
# إرجاع شرط زر الإنشاء إلى @can('manage warehouse') فقط
```

## ✨ **المميزات الجديدة**

- 🎯 **وصول مباشر** لأوامر الاستلام من قائمة POS
- ➕ **إنشاء أوامر جديدة** مع واجهة سهلة الاستخدام
- 📋 **عرض وإدارة** جميع أوامر الاستلام
- 🖨️ **طباعة وتحميل PDF** لأوامر الاستلام
- 🔒 **أمان محكم** مع التحقق من الصلاحيات
- 🎨 **تكامل سلس** مع التصميم الحالي
- ⚡ **أداء محسن** بدون تأثير على النظام
- 📦 **دعم جميع أنواع الأوامر** (استلام، نقل، إخراج)
