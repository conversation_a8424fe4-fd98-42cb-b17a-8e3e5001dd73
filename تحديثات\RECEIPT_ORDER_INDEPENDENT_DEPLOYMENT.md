# نشر نظام أوامر الاستلام المستقل

## 🎯 التحديث الجديد

تم تعديل نظام أوامر الاستلام ليعمل **بشكل مستقل** عن فواتير الشراء (`purchases` و `purchase_products`) والاكتفاء بتحديث `warehouse_products` مباشرة فقط.

## 🗃️ الجداول الجديدة

### 1. جدول `receipt_orders`
```sql
- id (Primary Key)
- order_number (رقم الأمر الفريد)
- order_type (نوع الأمر: استلام بضاعة / نقل بضاعة)
- vendor_id (المورد - اختياري)
- warehouse_id (المستودع الهدف)
- from_warehouse_id (المستودع المصدر - للنقل)
- invoice_number (رقم الفاتورة)
- invoice_total (إجمالي الفاتورة)
- invoice_date (تاريخ الفاتورة)
- has_return (هل يوجد مرتجع)
- total_products (عدد المنتجات)
- total_amount (إجمالي المبلغ)
- notes (ملاحظات)
- created_by (المنشئ)
- timestamps
```

### 2. جدول `receipt_order_products`
```sql
- id (Primary Key)
- receipt_order_id (معرف الأمر)
- product_id (معرف المنتج)
- quantity (الكمية)
- unit_cost (تكلفة الوحدة)
- total_cost (التكلفة الإجمالية)
- expiry_date (تاريخ الصلاحية)
- is_return (هل هو مرتجع)
- notes (ملاحظات)
- timestamps
```

## 🔄 التغييرات الرئيسية

### ❌ ما تم إلغاؤه:
- الربط مع جدول `purchases`
- الربط مع جدول `purchase_products`
- إنشاء فواتير شراء تلقائية

### ✅ ما تم الاحتفاظ به:
- تحديث `warehouse_products` مباشرة
- تحديث `product_expiry_dates`
- إنشاء `warehouse_transfers` للنقل
- جميع وظائف البحث والفلترة

## 📁 الملفات الجديدة/المحدثة

### 1. النماذج الجديدة:
```
app/Models/ReceiptOrder.php
app/Models/ReceiptOrderProduct.php
```

### 2. الكونترولر المحدث:
```
app/Http/Controllers/ReceiptOrderController.php
```

### 3. ملفات الهجرة:
```
database/migrations/2024_01_15_000001_create_receipt_orders_table.php
database/migrations/2024_01_15_000002_create_receipt_order_products_table.php
```

## 🚀 خطوات النشر

### الخطوة 1: رفع الملفات الجديدة
```bash
# رفع النماذج الجديدة
scp app/Models/ReceiptOrder.php user@server:/path/to/project/app/Models/
scp app/Models/ReceiptOrderProduct.php user@server:/path/to/project/app/Models/

# رفع الكونترولر المحدث
scp app/Http/Controllers/ReceiptOrderController.php user@server:/path/to/project/app/Http/Controllers/

# رفع ملفات الهجرة
scp database/migrations/2024_01_15_000001_create_receipt_orders_table.php user@server:/path/to/project/database/migrations/
scp database/migrations/2024_01_15_000002_create_receipt_order_products_table.php user@server:/path/to/project/database/migrations/
```

### الخطوة 2: تشغيل الهجرة
```bash
ssh user@server "cd /path/to/project && php artisan migrate"
```

### الخطوة 3: مسح الكاش
```bash
ssh user@server "cd /path/to/project && php artisan cache:clear"
ssh user@server "cd /path/to/project && php artisan config:clear"
ssh user@server "cd /path/to/project && php artisan route:clear"
```

## 🔧 كيف يعمل النظام الجديد

### **عند إنشاء أمر استلام بضاعة:**

1. **حفظ البيانات الأساسية**:
   ```php
   // إنشاء سجل في receipt_orders
   $receiptOrder = new ReceiptOrder();
   $receiptOrder->order_type = 'استلام بضاعة';
   $receiptOrder->vendor_id = $request->vendor_id;
   $receiptOrder->warehouse_id = $request->warehouse_id;
   // ... باقي البيانات
   ```

2. **حفظ المنتجات**:
   ```php
   // إنشاء سجل لكل منتج في receipt_order_products
   $receiptOrderProduct = new ReceiptOrderProduct();
   $receiptOrderProduct->receipt_order_id = $receiptOrder->id;
   $receiptOrderProduct->product_id = $productData['product_id'];
   $receiptOrderProduct->quantity = $productData['quantity'];
   // ... باقي البيانات
   ```

3. **تحديث المخزون مباشرة**:
   ```php
   // تحديث warehouse_products فقط
   $this->updateWarehouseStock(
       $request->warehouse_id,
       $productData['product_id'],
       $productData['quantity'],
       'add' // أو 'subtract' للمرتجعات
   );
   ```

### **عند نقل البضاعة:**

1. **إنشاء أمر نقل**:
   ```php
   $receiptOrder->order_type = 'نقل بضاعة';
   $receiptOrder->from_warehouse_id = $request->from_warehouse_id;
   $receiptOrder->warehouse_id = $request->warehouse_id;
   ```

2. **تحديث مخزون المستودعين**:
   ```php
   // خصم من المستودع المصدر
   $this->updateWarehouseStock($fromWarehouse, $product, $quantity, 'subtract');
   
   // إضافة للمستودع الهدف
   $this->updateWarehouseStock($toWarehouse, $product, $quantity, 'add');
   ```

## 📊 المزايا الجديدة

### 1. **استقلالية كاملة**:
- لا يعتمد على نظام فواتير الشراء
- إدارة مباشرة للمخزون
- مرونة أكبر في التعامل مع البيانات

### 2. **تتبع أفضل**:
- رقم أمر فريد لكل عملية
- تفاصيل كاملة لكل منتج
- تتبع المرتجعات بدقة

### 3. **أداء محسن**:
- استعلامات أسرع
- بيانات أقل تعقيداً
- فهرسة محسنة

## 🔍 التحقق من النظام

### اختبار استلام البضاعة:
- [ ] إنشاء أمر استلام جديد
- [ ] إضافة منتجات مع كميات
- [ ] تحديد تواريخ صلاحية
- [ ] معالجة المرتجعات
- [ ] التحقق من تحديث المخزون

### اختبار نقل البضاعة:
- [ ] إنشاء أمر نقل
- [ ] اختيار المستودع المصدر والهدف
- [ ] نقل منتجات
- [ ] التحقق من تحديث مخزون كلا المستودعين

### اختبار التقارير:
- [ ] عرض قائمة الأوامر
- [ ] فلترة بالنوع والمستودع
- [ ] عرض تفاصيل الأوامر
- [ ] إحصائيات شاملة

## 📈 الإحصائيات المتاحة

```php
// إجمالي أوامر الاستلام
$totalOrders = ReceiptOrder::count();

// أوامر استلام البضاعة
$receiptOrders = ReceiptOrder::where('order_type', 'استلام بضاعة')->count();

// أوامر نقل البضاعة
$transferOrders = ReceiptOrder::where('order_type', 'نقل بضاعة')->count();

// إجمالي القيمة المالية
$totalValue = ReceiptOrder::sum('total_amount');

// المنتجات المرتجعة
$returnedProducts = ReceiptOrderProduct::where('is_return', true)->count();
```

## 🎉 انتهاء التحديث

النظام الآن يعمل بشكل مستقل تماماً عن فواتير الشراء، مع الاحتفاظ بجميع الوظائف المطلوبة وتحسين الأداء والمرونة!

## 📝 ملاحظات مهمة

- **البيانات القديمة**: لن تتأثر البيانات الموجودة في `purchases` و `purchase_products`
- **التوافق**: النظام متوافق مع باقي أجزاء التطبيق
- **الأمان**: جميع العمليات محمية بالصلاحيات المناسبة
- **الأداء**: تحسن ملحوظ في سرعة العمليات
