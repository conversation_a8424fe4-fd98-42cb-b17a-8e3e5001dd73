@extends('layouts.admin')
@section('page-title')
    {{__('معالجة فواتير المستودع')}}
@endsection
@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{route('dashboard')}}">{{__('Dashboard')}}</a></li>
    <li class="breadcrumb-item">{{__('معالجة فواتير المستودع')}}</li>
@endsection

@push('css-page')
    <link rel="stylesheet" href="{{ asset('css/datatable/buttons.dataTables.min.css') }}">
    <style>
        .editable-field {
            cursor: pointer !important;
            border-bottom: 2px dashed #007bff !important;
            transition: all 0.3s ease;
            padding: 8px !important;
            position: relative;
        }
        .editable-field:hover {
            background-color: #e3f2fd !important;
            border-bottom: 2px solid #007bff !important;
            transform: scale(1.02);
        }
        .editing {
            background-color: #fff3cd !important;
            border: 2px solid #ffc107 !important;
        }
        .inline-edit-form {
            display: none;
        }
        .edit-icon {
            opacity: 0;
            transition: opacity 0.3s ease;
            margin-left: 5px;
            color: #007bff;
            font-size: 14px;
            font-weight: bold;
        }
        .editable-field:hover .edit-icon {
            opacity: 1;
        }
        .editable-field::before {
            content: "📝";
            position: absolute;
            top: 2px;
            right: 2px;
            font-size: 10px;
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        .editable-field:hover::before {
            opacity: 0.7;
        }
    </style>
@endpush

@section('action-btn')
    <div class="float-end">
        <a href="{{ route('purchase.create') }}" class="btn btn-sm btn-primary" data-bs-toggle="tooltip" title="{{__('Create New Purchase')}}">
            <i class="ti ti-plus"></i>
        </a>
    </div>
@endsection

@section('content')
    <!-- Inline Editing Info Alert -->
    <div class="row mb-3">
        <div class="col-12">
            <div class="alert alert-info alert-dismissible fade show" role="alert">
                <i class="ti ti-info-circle me-2"></i>
                <strong>{{__('ميزة التعديل المباشر:')}}</strong>
                {{__('يمكنك النقر على الحقول المميزة بخط متقطع (المورد، المستودع، تاريخ الشراء، الحالة) لتعديلها مباشرة دون الحاجة لفتح صفحة منفصلة.')}}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="theme-avtar bg-primary">
                            <i class="ti ti-shopping-cart"></i>
                        </div>
                        <div class="ms-3">
                            <small class="text-muted">{{__('إجمالي الفواتير')}}</small>
                            <h6 class="m-0">{{ $statistics['total_purchases'] ?? 0 }}</h6>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="theme-avtar bg-info">
                            <i class="ti ti-currency-dollar"></i>
                        </div>
                        <div class="ms-3">
                            <small class="text-muted">{{__('إجمالي المبلغ')}}</small>
                            <h6 class="m-0">
                                @php
                                    try {
                                        echo \Auth::user()->priceFormat($statistics['total_amount'] ?? 0);
                                    } catch (Exception $e) {
                                        echo '0.00';
                                    }
                                @endphp
                            </h6>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="theme-avtar bg-success">
                            <i class="ti ti-check"></i>
                        </div>
                        <div class="ms-3">
                            <small class="text-muted">{{__('المبلغ المدفوع')}}</small>
                            <h6 class="m-0">
                                @php
                                    try {
                                        echo \Auth::user()->priceFormat($statistics['total_paid'] ?? 0);
                                    } catch (Exception $e) {
                                        echo '0.00';
                                    }
                                @endphp
                            </h6>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="theme-avtar bg-danger">
                            <i class="ti ti-alert-circle"></i>
                        </div>
                        <div class="ms-3">
                            <small class="text-muted">{{__('المبلغ المستحق')}}</small>
                            <h6 class="m-0">
                                @php
                                    try {
                                        echo \Auth::user()->priceFormat($statistics['total_due'] ?? 0);
                                    } catch (Exception $e) {
                                        echo '0.00';
                                    }
                                @endphp
                            </h6>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <div class="row">
                        <div class="col-lg-6">
                            <h5>{{__('معالجة فواتير المستودع')}}</h5>
                            <p class="text-muted">{{__('إدارة ومعالجة جميع فواتير المشتريات الخاصة بالمستودع')}}</p>
                        </div>
                        <div class="col-lg-6">
                            <div class="text-end">
                                <div class="d-flex justify-content-end">
                                    <div class="btn-group" role="group">
                                        <button type="button" class="btn btn-outline-primary btn-sm" id="filterAll">{{__('الكل')}}</button>
                                        <button type="button" class="btn btn-outline-warning btn-sm" id="filterUnpaid">{{__('غير مدفوع')}}</button>
                                        <button type="button" class="btn btn-outline-success btn-sm" id="filterPaid">{{__('مدفوع')}}</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-body table-border-style">
                    <div class="table-responsive">
                        <table class="table datatable">
                            <thead>
                                <tr>
                                    <th>{{__('رقم الفاتورة')}}</th>
                                    <th>{{__('المورد')}}</th>
                                    <th>{{__('المستودع')}}</th>
                                    <th>{{__('تاريخ الشراء')}}</th>
                                    <th>{{__('المبلغ الإجمالي')}}</th>
                                    <th>{{__('المبلغ المدفوع')}}</th>
                                    <th>{{__('المبلغ المستحق')}}</th>
                                    <th>{{__('الحالة')}}</th>
                                    <th>{{__('الإجراءات')}}</th>
                                </tr>
                            </thead>
                            <tbody>
                                @if(isset($purchases) && count($purchases) > 0)
                                    @foreach ($purchases as $purchase)
                                        <tr data-purchase-id="{{ $purchase->id ?? '' }}">
                                            <td>
                                                <a href="{{ route('purchase.show', \Crypt::encrypt($purchase->id)) }}"
                                                   class="btn btn-outline-primary btn-sm">
                                                    {{ $purchase->purchase_id ?? 'N/A' }}
                                                </a>
                                            </td>
                                            <td class="editable-field" data-field="vender_id" data-type="select" data-value="{{ $purchase->vender_id ?? '' }}" data-original-text="{{ (isset($purchase->vender) && isset($purchase->vender->name)) ? $purchase->vender->name : '-' }}">
                                                {{ (isset($purchase->vender) && isset($purchase->vender->name)) ? $purchase->vender->name : '-' }}
                                                <i class="ti ti-edit edit-icon"></i>
                                            </td>
                                            <td class="editable-field" data-field="warehouse_id" data-type="select" data-value="{{ $purchase->warehouse_id ?? '' }}" data-original-text="{{ (isset($purchase->warehouse) && isset($purchase->warehouse->name)) ? $purchase->warehouse->name : '-' }}">
                                                {{ (isset($purchase->warehouse) && isset($purchase->warehouse->name)) ? $purchase->warehouse->name : '-' }}
                                                <i class="ti ti-edit edit-icon"></i>
                                            </td>
                                            <td class="editable-field" data-field="purchase_date" data-type="date" data-value="{{ $purchase->purchase_date ?? '' }}" data-original-text="{{ isset($purchase->purchase_date) ? \Auth::user()->dateFormat($purchase->purchase_date) : '-' }}">
                                                {{ isset($purchase->purchase_date) ? \Auth::user()->dateFormat($purchase->purchase_date) : '-' }}
                                                <i class="ti ti-edit edit-icon"></i>
                                            </td>
                                            <td>
                                                @php
                                                    try {
                                                        echo \Auth::user()->priceFormat($purchase->total_amount ?? 0);
                                                    } catch (Exception $e) {
                                                        echo '0.00';
                                                    }
                                                @endphp
                                            </td>
                                            <td>
                                                @php
                                                    try {
                                                        echo \Auth::user()->priceFormat($purchase->paid_amount ?? 0);
                                                    } catch (Exception $e) {
                                                        echo '0.00';
                                                    }
                                                @endphp
                                            </td>
                                            <td>
                                                @php
                                                    try {
                                                        echo \Auth::user()->priceFormat($purchase->due_amount ?? 0);
                                                    } catch (Exception $e) {
                                                        echo '0.00';
                                                    }
                                                @endphp
                                            </td>
                                            <td class="editable-field" data-field="status" data-type="select" data-value="{{ $purchase->status ?? 0 }}">
                                                @if(isset($purchase->status))
                                                    @if($purchase->status == 0)
                                                        <span class="badge bg-secondary">{{__('Draft')}}</span>
                                                    @elseif($purchase->status == 1)
                                                        <span class="badge bg-info">{{__('Sent')}}</span>
                                                    @elseif($purchase->status == 2)
                                                        <span class="badge bg-danger">{{__('Unpaid')}}</span>
                                                    @elseif($purchase->status == 3)
                                                        <span class="badge bg-warning">{{__('Partially Paid')}}</span>
                                                    @elseif($purchase->status == 4)
                                                        <span class="badge bg-success">{{__('Paid')}}</span>
                                                    @endif
                                                @else
                                                    <span class="badge bg-secondary">{{__('Unknown')}}</span>
                                                @endif
                                                <i class="ti ti-edit edit-icon"></i>
                                            </td>
                                            <td>
                                                <div class="action-btn bg-info ms-2">
                                                    <a href="{{ route('purchase.show', \Crypt::encrypt($purchase->id)) }}"
                                                       class="mx-3 btn btn-sm align-items-center"
                                                       data-bs-toggle="tooltip" title="{{__('View')}}">
                                                        <i class="ti ti-eye text-white"></i>
                                                    </a>
                                                </div>
                                                <div class="action-btn bg-success ms-2">
                                                    <a href="{{ route('warehouse.purchase.processing.edit.products', $purchase->id) }}"
                                                       class="mx-3 btn btn-sm align-items-center"
                                                       data-bs-toggle="tooltip" title="{{__('تعديل المنتجات')}}">
                                                        <i class="ti ti-package text-white"></i>
                                                    </a>
                                                </div>
                                                @if(isset($purchase->status) && $purchase->status != 4)
                                                    <div class="action-btn bg-warning ms-2">
                                                        <a href="{{ route('purchase.edit', \Crypt::encrypt($purchase->id)) }}"
                                                           class="mx-3 btn btn-sm align-items-center"
                                                           data-bs-toggle="tooltip" title="{{__('Edit')}}">
                                                            <i class="ti ti-pencil text-white"></i>
                                                        </a>
                                                    </div>
                                                @endif
                                            </td>
                                        </tr>
                                    @endforeach
                                @else
                                    <tr>
                                        <td colspan="9" class="text-center">
                                            <div class="alert alert-info">
                                                {{__('لا توجد فواتير مشتريات')}}
                                            </div>
                                        </td>
                                    </tr>
                                @endif
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('style-page')
    <style>
        .editable-field {
            cursor: pointer;
            border-bottom: 1px dashed #007bff;
            padding: 8px;
            position: relative;
            transition: all 0.3s ease;
            min-height: 35px;
            display: table-cell;
            vertical-align: middle;
        }

        .editable-field:hover {
            background-color: #f8f9fa;
            border-bottom: 2px dashed #0056b3;
            box-shadow: 0 2px 4px rgba(0,123,255,0.1);
        }

        .editable-field.editing {
            background-color: #e3f2fd !important;
            border: 2px solid #2196f3 !important;
            border-radius: 4px;
        }

        .edit-icon {
            font-size: 12px;
            margin-left: 5px;
            color: #007bff;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .editable-field:hover .edit-icon {
            opacity: 1;
        }

        .inline-editor {
            width: 100% !important;
            border: 2px solid #007bff !important;
            background: #fff !important;
            padding: 5px !important;
            border-radius: 4px !important;
            font-size: 14px !important;
        }

        .inline-editor:focus {
            outline: none !important;
            border-color: #0056b3 !important;
            box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25) !important;
        }

        .no-wrap {
            white-space: nowrap !important;
        }

        .datatable td {
            vertical-align: middle !important;
        }

        .save-edit, .cancel-edit {
            margin: 2px !important;
            padding: 4px 8px !important;
            font-size: 12px !important;
        }
    </style>
@endpush

@push('script-page')
    <script>
        $(document).ready(function() {
            // Setup CSRF token for all AJAX requests
            $.ajaxSetup({
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                }
            });

            // Debug information
            console.log('jQuery loaded:', typeof $ !== 'undefined');
            console.log('CSRF Token:', $('meta[name="csrf-token"]').attr('content'));
            console.log('Editable fields found:', $('.editable-field').length);

            // Initial setup for editable fields
            setupEditableHandlers();

            var table = $('.datatable').DataTable({
                "language": {
                    "url": "//cdn.datatables.net/plug-ins/1.10.25/i18n/Arabic.json"
                },
                "order": [[ 0, "desc" ]],
                "pageLength": 25,
                "responsive": false,
                "columnDefs": [
                    { "orderable": false, "targets": -1 },
                    { "className": "no-wrap", "targets": [1, 2, 3] }
                ],
                "drawCallback": function() {
                    console.log('DataTable redrawn. Editable fields found:', $('.editable-field').length);
                    if (currentEditingCell) {
                        currentEditingCell = null;
                    }
                }
            });

            // Filter buttons
            $('#filterAll').click(function() {
                table.column(7).search('').draw();
                $('.btn-group button').removeClass('btn-primary').addClass('btn-outline-primary');
                $(this).removeClass('btn-outline-primary').addClass('btn-primary');
            });

            $('#filterUnpaid').click(function() {
                table.column(7).search('Unpaid|Draft|Partially Paid', true, false).draw();
                $('.btn-group button').removeClass('btn-primary').addClass('btn-outline-primary');
                $(this).removeClass('btn-outline-primary').addClass('btn-primary');
            });

            $('#filterPaid').click(function() {
                table.column(7).search('Paid', true, false).draw();
                $('.btn-group button').removeClass('btn-primary').addClass('btn-outline-primary');
                $(this).removeClass('btn-outline-primary').addClass('btn-primary');
            });

            // Set default filter
            $('#filterAll').addClass('btn-primary').removeClass('btn-outline-primary');

            // Function to setup editable field handlers
            function setupEditableHandlers() {
                console.log('Setting up editable handlers...');
                $('.editable-field').off('click.inline-edit');
                $('.editable-field').on('click.inline-edit', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    console.log('Editable field clicked');
                    handleEditableClick($(this));
                });
                console.log('Handlers setup complete. Fields with handlers:', $('.editable-field').length);
            }

            // Inline editing functionality
            let currentEditingCell = null;

            // Function to handle editable field clicks
            function handleEditableClick($cell) {
                console.log('=== Editable field clicked! ===');

                if (currentEditingCell && currentEditingCell[0] !== $cell[0]) {
                    console.log('Canceling previous edit...');
                    cancelEdit();
                }

                if (currentEditingCell && currentEditingCell[0] === $cell[0]) {
                    console.log('Already editing this cell');
                    return;
                }

                const field = $cell.data('field');
                const type = $cell.data('type');
                const value = $cell.data('value');
                const $row = $cell.closest('tr');
                const purchaseId = $row.data('purchase-id');

                console.log('Field data:', { field, type, value, purchaseId });

                if (!field) {
                    console.error('Field is missing!');
                    showToast('error', 'خطأ: معرف الحقل مفقود');
                    return;
                }
                if (!purchaseId) {
                    console.error('Purchase ID is missing!');
                    showToast('error', 'خطأ: معرف الفاتورة مفقود');
                    return;
                }

                console.log('Validation passed. Starting edit...');
                currentEditingCell = $cell;
                $cell.addClass('editing');

                if (!$cell.data('original-text')) {
                    const originalText = $cell.clone().children('.edit-icon').remove().end().text().trim();
                    $cell.data('original-text', originalText);
                }

                if (type === 'select') {
                    console.log('Creating select editor...');
                    createSelectEditor($cell, field, value, purchaseId);
                } else if (type === 'date') {
                    console.log('Creating date editor...');
                    createDateEditor($cell, field, value, purchaseId);
                } else {
                    console.log('Creating text editor...');
                    createTextEditor($cell, field, value, purchaseId);
                }
            }

            // Handle click on editable fields
            $(document).on('click', '.editable-field', function(e) {
                e.preventDefault();
                e.stopPropagation();
                console.log('Editable field clicked!');
                handleEditableClick($(this));
            });

            // Show toast notification
            function showToast(type, message) {
                if (typeof show_toastr === 'function') {
                    show_toastr(type, message);
                } else if (typeof toastr !== 'undefined') {
                    toastr[type](message);
                } else {
                    alert(message);
                }
            }

            // Cancel edit function
            function cancelEdit() {
                if (currentEditingCell) {
                    console.log('Canceling edit');
                    const $cell = currentEditingCell;
                    const originalText = $cell.data('original-text') || '';
                    $cell.html(originalText + '<i class="ti ti-edit edit-icon"></i>');
                    $cell.removeClass('editing');
                    currentEditingCell = null;
                }
            }

            // Simplified editor functions
            function createSelectEditor($cell, field, value, purchaseId) {
                let selectHtml = '<select class="form-control form-control-sm inline-editor">';
                selectHtml += '<option value="">اختر...</option>';
                selectHtml += '</select>';
                selectHtml += '<div class="mt-1"><button class="btn btn-sm btn-success save-edit" type="button"><i class="ti ti-check"></i></button> ';
                selectHtml += '<button class="btn btn-sm btn-secondary cancel-edit" type="button"><i class="ti ti-x"></i></button></div>';
                $cell.html(selectHtml);
                $cell.find('.inline-editor').focus();
            }

            function createDateEditor($cell, field, value, purchaseId) {
                let inputHtml = `<input type="date" class="form-control form-control-sm inline-editor" value="${value}">`;
                inputHtml += '<div class="mt-1"><button class="btn btn-sm btn-success save-edit" type="button"><i class="ti ti-check"></i></button> ';
                inputHtml += '<button class="btn btn-sm btn-secondary cancel-edit" type="button"><i class="ti ti-x"></i></button></div>';
                $cell.html(inputHtml);
                $cell.find('.inline-editor').focus();
            }

            function createTextEditor($cell, field, value, purchaseId) {
                let inputHtml = `<input type="text" class="form-control form-control-sm inline-editor" value="${value}">`;
                inputHtml += '<div class="mt-1"><button class="btn btn-sm btn-success save-edit" type="button"><i class="ti ti-check"></i></button> ';
                inputHtml += '<button class="btn btn-sm btn-secondary cancel-edit" type="button"><i class="ti ti-x"></i></button></div>';
                $cell.html(inputHtml);
                $cell.find('.inline-editor').focus();
            }

            // Handle save edit
            $(document).on('click', '.save-edit', function(e) {
                e.preventDefault();
                e.stopPropagation();
                console.log('Save button clicked');
                const $cell = currentEditingCell;
                const field = $cell.data('field');
                const newValue = $cell.find('.inline-editor').val();
                const purchaseId = $cell.closest('tr').data('purchase-id');

                if (!newValue) {
                    showToast('error', 'القيمة لا يمكن أن تكون فارغة');
                    return;
                }

                $cell.find('.save-edit').html('<i class="ti ti-loader"></i>').prop('disabled', true);

                console.log('Sending update request:', { purchaseId, field, newValue });
                $.post('{{ route("warehouse.purchase.processing.update.inline") }}', {
                    _token: '{{ csrf_token() }}',
                    purchase_id: purchaseId,
                    field: field,
                    value: newValue
                })
                .done(function(response) {
                    console.log('Update response:', response);
                    if (response.success) {
                        $cell.html(response.display_value + '<i class="ti ti-edit edit-icon"></i>');
                        $cell.data('value', newValue);
                        showToast('success', response.message);

                        if (field === 'status') {
                            setTimeout(() => location.reload(), 1000);
                        }
                    } else {
                        console.error('Update failed:', response);
                        showToast('error', response.message);
                        cancelEdit();
                    }
                })
                .fail(function(xhr, status, error) {
                    console.error('Update AJAX Error:', { xhr, status, error, responseText: xhr.responseText });
                    showToast('error', 'حدث خطأ أثناء التحديث');
                    cancelEdit();
                })
                .always(function() {
                    currentEditingCell = null;
                    $cell.removeClass('editing');
                });
            });

            // Handle cancel edit
            $(document).on('click', '.cancel-edit', function(e) {
                e.preventDefault();
                e.stopPropagation();
                console.log('Cancel button clicked');
                cancelEdit();
            });

            // Handle escape key
            $(document).on('keydown', '.inline-editor', function(e) {
                if (e.key === 'Escape') {
                    cancelEdit();
                } else if (e.key === 'Enter') {
                    $('.save-edit').click();
                }
            });
        });
    </script>
@endpush
