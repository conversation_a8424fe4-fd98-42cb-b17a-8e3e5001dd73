<?php
/**
 * سكريبت تشخيص مشكلة الفواتير غير المدفوعة
 * يجب تشغيل هذا من خلال المتصفح للحصول على معلومات مفصلة
 */

// تحديد مسار Laravel
$laravel_path = __DIR__;
require_once $laravel_path . '/vendor/autoload.php';

// تحميل Laravel
$app = require_once $laravel_path . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Http\Kernel::class);
$response = $kernel->handle(
    $request = Illuminate\Http\Request::capture()
);

?>
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تشخيص مشكلة الفواتير غير المدفوعة</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .warning { background-color: #fff3cd; border-color: #ffeaa7; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        h2 { color: #333; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }
        th { background-color: #f8f9fa; }
        .sql-code { background-color: #2d3748; color: #e2e8f0; padding: 15px; border-radius: 5px; font-family: 'Courier New', monospace; }
        .badge { padding: 4px 8px; border-radius: 4px; color: white; font-size: 12px; }
        .badge-success { background-color: #28a745; }
        .badge-danger { background-color: #dc3545; }
        .badge-warning { background-color: #ffc107; color: #212529; }
    </style>
</head>
<body>
    <h1>🔍 تشخيص مشكلة الفواتير غير المدفوعة</h1>
    
    <?php
    try {
        // الاتصال بقاعدة البيانات
        $pdo = new PDO(
            'mysql:host=' . env('DB_HOST') . ';dbname=' . env('DB_DATABASE'),
            env('DB_USERNAME'),
            env('DB_PASSWORD')
        );
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        echo '<div class="section success"><h2>✅ تم الاتصال بقاعدة البيانات بنجاح</h2></div>';
        
        // فحص آخر 10 فواتير
        echo '<div class="section info">';
        echo '<h2>📊 آخر 10 فواتير POS</h2>';
        
        $stmt = $pdo->query("
            SELECT 
                p.id,
                p.pos_id,
                p.pos_date,
                p.customer_id,
                p.warehouse_id,
                pp.id as payment_id,
                pp.payment_type,
                pp.amount as payment_amount,
                CASE 
                    WHEN pp.payment_type IS NOT NULL AND pp.payment_type != '' THEN 'مدفوع'
                    ELSE 'غير مدفوع'
                END as status
            FROM pos p 
            LEFT JOIN pos_payments pp ON p.id = pp.pos_id 
            ORDER BY p.id DESC 
            LIMIT 10
        ");
        
        $invoices = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if ($invoices) {
            echo '<table>';
            echo '<tr><th>معرف الفاتورة</th><th>رقم الفاتورة</th><th>التاريخ</th><th>معرف الدفع</th><th>نوع الدفع</th><th>المبلغ</th><th>الحالة</th></tr>';
            
            foreach ($invoices as $invoice) {
                $statusClass = $invoice['status'] == 'مدفوع' ? 'badge-success' : 'badge-danger';
                echo '<tr>';
                echo '<td>' . $invoice['id'] . '</td>';
                echo '<td>' . $invoice['pos_id'] . '</td>';
                echo '<td>' . $invoice['pos_date'] . '</td>';
                echo '<td>' . ($invoice['payment_id'] ?? 'لا يوجد') . '</td>';
                echo '<td>' . ($invoice['payment_type'] ?? 'لا يوجد') . '</td>';
                echo '<td>' . ($invoice['payment_amount'] ?? 'لا يوجد') . '</td>';
                echo '<td><span class="badge ' . $statusClass . '">' . $invoice['status'] . '</span></td>';
                echo '</tr>';
            }
            echo '</table>';
        }
        echo '</div>';
        
        // إحصائيات عامة
        echo '<div class="section warning">';
        echo '<h2>📈 إحصائيات عامة</h2>';
        
        $stats = $pdo->query("
            SELECT 
                (SELECT COUNT(*) FROM pos) as total_invoices,
                (SELECT COUNT(*) FROM pos_payments) as total_payments,
                (SELECT COUNT(*) FROM pos p LEFT JOIN pos_payments pp ON p.id = pp.pos_id WHERE pp.id IS NULL) as missing_payments,
                (SELECT COUNT(*) FROM pos_payments WHERE payment_type IS NULL OR payment_type = '') as empty_payment_types
        ")->fetch(PDO::FETCH_ASSOC);
        
        echo '<table>';
        echo '<tr><th>الإحصائية</th><th>العدد</th></tr>';
        echo '<tr><td>إجمالي الفواتير</td><td>' . $stats['total_invoices'] . '</td></tr>';
        echo '<tr><td>إجمالي المدفوعات</td><td>' . $stats['total_payments'] . '</td></tr>';
        echo '<tr><td>فواتير بدون دفع</td><td>' . $stats['missing_payments'] . '</td></tr>';
        echo '<tr><td>مدفوعات بدون نوع</td><td>' . $stats['empty_payment_types'] . '</td></tr>';
        echo '</table>';
        echo '</div>';
        
        // فحص آخر فاتورة تم إنشاؤها
        echo '<div class="section info">';
        echo '<h2>🔍 تفاصيل آخر فاتورة</h2>';
        
        $lastInvoice = $pdo->query("
            SELECT 
                p.*,
                pp.payment_type,
                pp.amount as payment_amount,
                pp.cash_amount,
                pp.network_amount,
                c.name as customer_name,
                w.name as warehouse_name
            FROM pos p 
            LEFT JOIN pos_payments pp ON p.id = pp.pos_id 
            LEFT JOIN customers c ON p.customer_id = c.id
            LEFT JOIN warehouses w ON p.warehouse_id = w.id
            ORDER BY p.id DESC 
            LIMIT 1
        ")->fetch(PDO::FETCH_ASSOC);
        
        if ($lastInvoice) {
            echo '<table>';
            foreach ($lastInvoice as $key => $value) {
                echo '<tr><td><strong>' . $key . '</strong></td><td>' . ($value ?? 'NULL') . '</td></tr>';
            }
            echo '</table>';
            
            // فحص منتجات آخر فاتورة
            echo '<h3>منتجات آخر فاتورة:</h3>';
            $products = $pdo->query("
                SELECT 
                    pp.*,
                    ps.name as product_name,
                    ps.type as product_type
                FROM pos_products pp 
                LEFT JOIN product_services ps ON pp.product_id = ps.id
                WHERE pp.pos_id = " . $lastInvoice['id']
            )->fetchAll(PDO::FETCH_ASSOC);
            
            if ($products) {
                echo '<table>';
                echo '<tr><th>اسم المنتج</th><th>النوع</th><th>الكمية</th><th>السعر</th><th>المجموع</th></tr>';
                foreach ($products as $product) {
                    echo '<tr>';
                    echo '<td>' . $product['product_name'] . '</td>';
                    echo '<td>' . $product['product_type'] . '</td>';
                    echo '<td>' . $product['quantity'] . '</td>';
                    echo '<td>' . $product['price'] . '</td>';
                    echo '<td>' . ($product['price'] * $product['quantity']) . '</td>';
                    echo '</tr>';
                }
                echo '</table>';
            }
        }
        echo '</div>';
        
        // فحص سجلات stock_reports
        echo '<div class="section info">';
        echo '<h2>📋 آخر سجلات stock_reports</h2>';
        
        $stockReports = $pdo->query("
            SELECT 
                sr.*,
                ps.name as product_name,
                ps.type as product_type
            FROM stock_reports sr 
            LEFT JOIN product_services ps ON sr.product_id = ps.id
            ORDER BY sr.id DESC 
            LIMIT 10
        ")->fetchAll(PDO::FETCH_ASSOC);
        
        if ($stockReports) {
            echo '<table>';
            echo '<tr><th>المنتج</th><th>النوع</th><th>الكمية</th><th>نوع العملية</th><th>الوصف</th><th>التاريخ</th></tr>';
            foreach ($stockReports as $report) {
                echo '<tr>';
                echo '<td>' . $report['product_name'] . '</td>';
                echo '<td>' . $report['product_type'] . '</td>';
                echo '<td>' . $report['quantity'] . '</td>';
                echo '<td>' . $report['type'] . '</td>';
                echo '<td>' . $report['description'] . '</td>';
                echo '<td>' . $report['created_at'] . '</td>';
                echo '</tr>';
            }
            echo '</table>';
        }
        echo '</div>';
        
    } catch (Exception $e) {
        echo '<div class="section error">';
        echo '<h2>❌ خطأ في الاتصال</h2>';
        echo '<p>رسالة الخطأ: ' . $e->getMessage() . '</p>';
        echo '</div>';
    }
    ?>
    
    <div class="section warning">
        <h2>🔧 خطوات التشخيص التالية</h2>
        <ol>
            <li>تحقق من النتائج أعلاه</li>
            <li>إذا كانت هناك فواتير "غير مدفوع"، نفذ أمر الإصلاح</li>
            <li>تحقق من وجود أخطاء في سجلات Laravel</li>
            <li>اختبر إنشاء فاتورة جديدة</li>
        </ol>
    </div>
    
    <script>
        console.log('🔍 تم تحميل صفحة التشخيص بنجاح');
    </script>
</body>
</html>
