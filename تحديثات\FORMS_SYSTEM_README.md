# نظام النماذج - Forms System

## 📋 الوصف
تم إنشاء نظام النماذج في الشاشة الرئيسية يسمح للمستخدمين من نوع "company" بإنشاء نماذج (تشغيلية أو مالية) ورفعها كملفات PDF، مع تحديد من يمكنه رؤية كل نموذج من الأدوار المختلفة.

## ✨ المميزات

### للمستخدمين من نوع Company:
- إنشاء نماذج جديدة (تشغيلية أو مالية)
- رفع ملفات PDF بأي حجم
- تحديد الأدوار التي يمكنها رؤية النموذج:
  - كاشير (Cashier)
  - سوبر فايزر (Supervisor) 
  - دليفري (Delivery)
  - محاسب (Accountant)
  - الجميع (All)
- حذف النماذج التي أنشأوها

### لجميع المستخدمين:
- عرض النماذج المتاحة لهم في القائمة الجانبية والشاشة الرئيسية
- تصفح النماذج مقسمة حسب النوع (تشغيلية/مالية)
- صفحة منفصلة لعرض جميع النماذج بتفاصيل كاملة
- فتح النماذج في نافذة جديدة للعرض

## 🗂️ الملفات المضافة

### 1. Migration
```
database/migrations/2024_01_01_000000_create_forms_table.php
```

### 2. Model
```
app/Models/Form.php
```

### 3. Controller
```
app/Http/Controllers/FormController.php
```

### 4. Views
```
resources/views/forms/create.blade.php
resources/views/forms/index.blade.php
resources/views/forms/dashboard-section.blade.php
```

## 🔧 الملفات المحدثة

### 1. DashboardController
```php
// إضافة استيراد النموذج
use App\Models\Form;

// إضافة النماذج للعرض
$forms = Form::getVisibleForms();
```

### 2. dashboard.blade.php
```php
// إضافة قسم النماذج
@include('forms.dashboard-section')
```

### 3. routes/web.php
```php
// إضافة routes النماذج
Route::resource('forms', FormController::class)->except(['edit', 'update']);
Route::get('forms/{form}', [FormController::class, 'show'])->name('forms.show');
```

### 4. menu.blade.php
```php
// إضافة قسم النماذج في القائمة الجانبية
<li class="dash-item dash-hasmenu">
    <a href="#!" class="dash-link">
        <span class="dash-micon"><i class="ti ti-file-text"></i></span>
        <span class="dash-mtext">{{ __('النماذج') }}</span>
        <span class="dash-arrow"><i data-feather="chevron-right"></i></span>
    </a>
    <ul class="dash-submenu">
        <li><a href="{{ route('forms.index') }}">{{ __('عرض النماذج') }}</a></li>
        @if(Auth::user()->hasRole('company'))
            <li><a href="{{ route('forms.create') }}">{{ __('إنشاء نموذج جديد') }}</a></li>
        @endif
    </ul>
</li>
```

## 🗄️ هيكل قاعدة البيانات

### جدول forms
```sql
- id (bigint, primary key)
- name (string) - اسم النموذج
- type (enum: 'operational', 'financial') - نوع النموذج
- file_path (string) - مسار الملف
- visible_to_roles (json) - الأدوار المسموح لها بالرؤية
- created_by (integer) - معرف المنشئ
- created_at (timestamp)
- updated_at (timestamp)
```

## 🚀 التثبيت والتشغيل

### 1. نقل الملفات للسيرفر
```bash
# الملفات الجديدة
database/migrations/2024_01_01_000000_create_forms_table.php
app/Models/Form.php
app/Http/Controllers/FormController.php
resources/views/forms/create.blade.php
resources/views/forms/index.blade.php
resources/views/forms/dashboard-section.blade.php

# الملفات المحدثة
app/Http/Controllers/DashboardController.php
resources/views/dashboard/dashboard.blade.php
resources/views/partials/admin/menu.blade.php
routes/web.php
```

### 2. تشغيل Migration
```bash
php artisan migrate
```

### 3. إنشاء مجلد النماذج
```bash
mkdir -p storage/app/public/forms
```

### 4. ربط التخزين (إذا لم يكن موجود)
```bash
php artisan storage:link
```

## 📱 كيفية الاستخدام

### للمستخدمين من نوع Company:

1. **تسجيل الدخول** كمستخدم من نوع 'company'
2. **الذهاب للشاشة الرئيسية** (Dashboard)
3. **العثور على قسم النماذج** في أعلى الصفحة
4. **الضغط على "إنشاء نموذج جديد"**
5. **ملء البيانات المطلوبة:**
   - اختيار نوع النموذج (تشغيل/مالي)
   - إدخال اسم النموذج
   - رفع ملف PDF
   - تحديد من يمكنه رؤية النموذج
6. **الضغط على "إنشاء النموذج"**

### للمستخدمين الآخرين:

1. **تسجيل الدخول** بأي دور (كاشير، سوبر فايزر، دليفري، محاسب)
2. **الذهاب للقائمة الجانبية** واختيار "النماذج" > "عرض النماذج"
3. **تصفح النماذج** المتاحة لك مقسمة حسب النوع (تشغيلية/مالية)
4. **الضغط على أيقونة العين** لعرض النموذج في نافذة جديدة

## 🔒 نظام الصلاحيات

- **إنشاء النماذج:** مستخدمي company فقط
- **عرض النماذج:** حسب الأدوار المحددة لكل نموذج
- **حذف النماذج:** منشئ النموذج أو مستخدمي company

## 🎨 التصميم

- **تصميم متجاوب** يتناسب مع جميع الشاشات
- **ألوان مميزة** للنماذج التشغيلية (أزرق) والمالية (أخضر)
- **أيقونات واضحة** لسهولة الاستخدام
- **تنظيم منطقي** للنماذج حسب النوع

## 🧪 الاختبار

يمكن تشغيل ملف الاختبار للتحقق من صحة التثبيت:
```
http://your-domain.com/test_forms_system.php
```

## 📞 الدعم

في حالة وجود أي مشاكل أو استفسارات، يرجى التواصل مع فريق التطوير.
