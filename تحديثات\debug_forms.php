<?php
/**
 * صفحة تشخيص النماذج والأدوار
 */

// تحديد المسار الأساسي
$basePath = __DIR__;

echo "<!DOCTYPE html>";
echo "<html dir='rtl' lang='ar'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<title>تشخيص النماذج والأدوار</title>";
echo "<style>";
echo "body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }";
echo ".container { max-width: 1000px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }";
echo ".success { color: #28a745; background: #d4edda; padding: 10px; border-radius: 4px; margin: 10px 0; }";
echo ".error { color: #dc3545; background: #f8d7da; padding: 10px; border-radius: 4px; margin: 10px 0; }";
echo ".info { color: #17a2b8; background: #d1ecf1; padding: 10px; border-radius: 4px; margin: 10px 0; }";
echo ".warning { color: #856404; background: #fff3cd; padding: 10px; border-radius: 4px; margin: 10px 0; }";
echo "table { width: 100%; border-collapse: collapse; margin: 10px 0; }";
echo "th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }";
echo "th { background: #f8f9fa; }";
echo ".code { background: #f8f9fa; padding: 10px; border-radius: 4px; font-family: monospace; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<div class='container'>";
echo "<h1>🔍 تشخيص النماذج والأدوار</h1>";

// فحص قاعدة البيانات
try {
    // محاولة الاتصال بقاعدة البيانات
    $pdo = new PDO("mysql:host=localhost;dbname=up20251", "root", "");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<div class='success'>✅ تم الاتصال بقاعدة البيانات بنجاح</div>";
    
    // فحص جدول النماذج
    echo "<div class='info'>";
    echo "<h2>📋 النماذج الموجودة في قاعدة البيانات:</h2>";
    
    $stmt = $pdo->query("SELECT * FROM forms ORDER BY created_at DESC");
    $forms = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (count($forms) > 0) {
        echo "<table>";
        echo "<tr><th>ID</th><th>الاسم</th><th>النوع</th><th>الأدوار المسموح لها</th><th>منشئ النموذج</th><th>تاريخ الإنشاء</th></tr>";
        
        foreach ($forms as $form) {
            $visibleRoles = json_decode($form['visible_to_roles'], true);
            $rolesText = is_array($visibleRoles) ? implode(', ', $visibleRoles) : 'غير محدد';
            
            echo "<tr>";
            echo "<td>{$form['id']}</td>";
            echo "<td>{$form['name']}</td>";
            echo "<td>" . ($form['type'] == 'operational' ? 'تشغيل' : 'مالي') . "</td>";
            echo "<td>{$rolesText}</td>";
            echo "<td>{$form['created_by']}</td>";
            echo "<td>{$form['created_at']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<div class='warning'>⚠️ لا توجد نماذج في قاعدة البيانات</div>";
    }
    echo "</div>";
    
    // فحص المستخدمين والأدوار
    echo "<div class='info'>";
    echo "<h2>👥 المستخدمين والأدوار:</h2>";
    
    $stmt = $pdo->query("
        SELECT u.id, u.name, u.email, u.type, 
               GROUP_CONCAT(r.name) as roles
        FROM users u
        LEFT JOIN model_has_roles mhr ON u.id = mhr.model_id AND mhr.model_type = 'App\\\\Models\\\\User'
        LEFT JOIN roles r ON mhr.role_id = r.id
        WHERE u.type IN ('company', 'accountant', 'Employee')
        GROUP BY u.id
        ORDER BY u.type, u.name
    ");
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (count($users) > 0) {
        echo "<table>";
        echo "<tr><th>ID</th><th>الاسم</th><th>البريد</th><th>النوع</th><th>الأدوار</th></tr>";
        
        foreach ($users as $user) {
            echo "<tr>";
            echo "<td>{$user['id']}</td>";
            echo "<td>{$user['name']}</td>";
            echo "<td>{$user['email']}</td>";
            echo "<td>{$user['type']}</td>";
            echo "<td>" . ($user['roles'] ?: 'لا توجد أدوار') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<div class='warning'>⚠️ لا توجد مستخدمين</div>";
    }
    echo "</div>";
    
    // فحص الأدوار المتاحة
    echo "<div class='info'>";
    echo "<h2>🎭 الأدوار المتاحة في النظام:</h2>";
    
    $stmt = $pdo->query("SELECT * FROM roles ORDER BY name");
    $roles = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (count($roles) > 0) {
        echo "<table>";
        echo "<tr><th>ID</th><th>اسم الدور</th><th>منشئ الدور</th></tr>";
        
        foreach ($roles as $role) {
            echo "<tr>";
            echo "<td>{$role['id']}</td>";
            echo "<td>{$role['name']}</td>";
            echo "<td>{$role['created_by']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    echo "</div>";
    
} catch (PDOException $e) {
    echo "<div class='error'>❌ خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage() . "</div>";
}

// إرشادات الاختبار
echo "<div class='success'>";
echo "<h2>🧪 خطوات الاختبار:</h2>";
echo "<ol>";
echo "<li><strong>تسجيل الدخول:</strong> سجل دخول كمستخدم من نوع 'company'</li>";
echo "<li><strong>إنشاء نموذج:</strong> اذهب لإنشاء نموذج جديد</li>";
echo "<li><strong>اختيار الأدوار:</strong> حدد الأدوار التي يمكنها رؤية النموذج</li>";
echo "<li><strong>حفظ النموذج:</strong> احفظ النموذج وتحقق من ظهوره في القائمة</li>";
echo "<li><strong>اختبار الرؤية:</strong> سجل دخول بمستخدمين مختلفين وتحقق من الرؤية</li>";
echo "</ol>";
echo "</div>";

echo "<div class='warning'>";
echo "<h2>⚠️ نصائح لحل المشاكل:</h2>";
echo "<ul>";
echo "<li>تأكد من أن المستخدم من نوع 'company' وليس دور 'company'</li>";
echo "<li>تأكد من اختيار الأدوار الصحيحة عند إنشاء النموذج</li>";
echo "<li>تحقق من أن الأدوار المحددة موجودة في النظام</li>";
echo "<li>في حالة عدم ظهور النماذج، تحقق من logs في storage/logs/laravel.log</li>";
echo "</ul>";
echo "</div>";

echo "<div class='info'>";
echo "<h2>🔧 أوامر مفيدة:</h2>";
echo "<div class='code'>";
echo "# مسح الكاش<br>";
echo "php artisan cache:clear<br>";
echo "php artisan route:clear<br>";
echo "php artisan view:clear<br><br>";

echo "# فحص المسارات<br>";
echo "php artisan route:list | grep forms<br><br>";

echo "# فحص قاعدة البيانات<br>";
echo "SELECT * FROM forms;<br>";
echo "SELECT * FROM users WHERE type = 'company';<br>";
echo "SELECT * FROM roles;<br>";
echo "</div>";
echo "</div>";

echo "</div>";
echo "</body>";
echo "</html>";
?>
