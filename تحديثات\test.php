<?php
echo "<h1>🚀 اختبار الخادم - المجلد الجذر</h1>";
echo "<p>إذا رأيت هذه الرسالة، فإن PHP يعمل بشكل صحيح!</p>";
echo "<p><strong>الوقت الحالي:</strong> " . date('Y-m-d H:i:s') . "</p>";
echo "<p><strong>إصدار PHP:</strong> " . PHP_VERSION . "</p>";
echo "<p><strong>مسار الملف:</strong> " . __FILE__ . "</p>";

// اختبار قاعدة البيانات
try {
    $pdo = new PDO('mysql:host=127.0.0.1;port=3306;dbname=ty', 'root', '');
    echo "<p style='color: green;'>✅ الاتصال بقاعدة البيانات ناجح!</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ في قاعدة البيانات: " . $e->getMessage() . "</p>";
}
?>
