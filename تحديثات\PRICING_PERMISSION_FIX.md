# 🔧 حل مشكلة صلاحيات التسعير - Permission Denied

## 🎯 المشكلة
عند النقر على قسم "التسعير" تظهر رسالة "Permission denied" لأن المستخدم الحالي لا يملك الصلاحيات المطلوبة.

## ✅ الحلول المتاحة

### **الحل الأول: تشغيل ملف SQL لإعطاء الصلاحيات (الأسرع)**

1. **افتح قاعدة البيانات** (phpMyAdmin أو أي أداة إدارة قواعد البيانات)

2. **شغل الملف** `fix_pricing_permissions.sql` الذي تم إنشاؤه

3. **أعد تسجيل الدخول** للنظام

### **الحل الثاني: التعديلات المطبقة على الكود (مطبق بالفعل)**

تم تعديل `PricingController.php` لجعل قسم التسعير متاحاً للمستخدمين التاليين:
- مستخدمي الشركة (`company`)
- مستخدمي المحاسبة (`accountant`) 
- مستخدمي التسعير (`Pricing`)
- أي مستخدم لديه صلاحية `manage pricing`
- أي مستخدم لديه صلاحية `manage product & service`

## 📁 الملفات المحدثة

### 1. **PricingController.php** ✅ (محدث)
```php
// التحقق من الصلاحيات - متاح لمستخدمي الشركة والمحاسبة والتسعير
if (!Auth::user()->hasRole('company') && 
    !Auth::user()->hasRole('accountant') && 
    !Auth::user()->hasRole('Pricing') && 
    !Auth::user()->can('manage pricing') &&
    !Auth::user()->can('manage product & service')) {
    return redirect()->back()->with('error', __('Permission denied.'));
}
```

### 2. **menu.blade.php** ✅ (محدث)
- قسم "إدارة العمليات المالية" يفتح تلقائياً عند وجود صلاحيات التسعير
- قسم "التسعير" مرئي للمستخدمين المخولين

### 3. **fix_pricing_permissions.sql** ✅ (جديد)
- ملف SQL لإعطاء صلاحيات التسعير للمستخدمين

## 🚀 خطوات النشر

### للخادم المحلي (Laragon):
```bash
# 1. نسخ الملفات المحدثة
app/Http/Controllers/PricingController.php
resources/views/partials/admin/menu.blade.php

# 2. تشغيل ملف SQL
fix_pricing_permissions.sql
```

### للخادم المباشر (Cloudways):
```bash
# 1. رفع الملفات عبر FTP/SFTP
app/Http/Controllers/PricingController.php
resources/views/partials/admin/menu.blade.php

# 2. تشغيل ملف SQL عبر phpMyAdmin
fix_pricing_permissions.sql

# 3. مسح الـ cache
php artisan cache:clear
php artisan config:clear
php artisan route:clear
```

## 🔍 التحقق من الحل

### 1. **تحقق من الصلاحيات في قاعدة البيانات:**
```sql
-- عرض المستخدمين الذين لديهم صلاحية manage pricing
SELECT u.name, u.email, u.type
FROM users u
INNER JOIN model_has_permissions mhp ON u.id = mhp.model_id
INNER JOIN permissions p ON mhp.permission_id = p.id
WHERE p.name = 'manage pricing';
```

### 2. **تحقق من الأدوار:**
```sql
-- عرض المستخدمين وأدوارهم
SELECT u.name, u.email, r.name as role_name
FROM users u
INNER JOIN model_has_roles mhr ON u.id = mhr.model_id
INNER JOIN roles r ON mhr.role_id = r.id
WHERE r.name IN ('company', 'accountant', 'Pricing');
```

### 3. **اختبار الوصول:**
- سجل دخول بحساب مستخدم الشركة
- انتقل إلى القائمة الجانبية
- تحقق من أن قسم "إدارة العمليات المالية" مفتوح
- انقر على "التسعير"
- يجب أن تفتح الصفحة بدون أخطاء

## 🛠️ استكشاف الأخطاء

### خطأ: "Route not found"
```bash
php artisan route:clear
php artisan cache:clear
```

### خطأ: "Permission denied" ما زال موجود
1. تحقق من تشغيل ملف SQL بنجاح
2. تحقق من دور المستخدم الحالي
3. أعد تسجيل الدخول

### خطأ: القائمة لا تفتح تلقائياً
1. تحقق من تحديث ملف `menu.blade.php`
2. مسح cache المتصفح
3. إعادة تحميل الصفحة

## 📊 الصلاحيات المطلوبة

| الدور | الصلاحيات المطلوبة |
|-------|-------------------|
| **company** | تلقائي - لا يحتاج صلاحيات إضافية |
| **accountant** | تلقائي - لا يحتاج صلاحيات إضافية |
| **Pricing** | `manage pricing`, `edit pricing`, `show pricing` |
| **مستخدم عادي** | `manage product & service` |

## ✅ قائمة التحقق النهائية

- [ ] تم تحديث `PricingController.php`
- [ ] تم تحديث `menu.blade.php`
- [ ] تم تشغيل `fix_pricing_permissions.sql`
- [ ] تم مسح الـ cache
- [ ] تم اختبار الوصول لصفحة التسعير
- [ ] قسم "إدارة العمليات المالية" يفتح تلقائياً
- [ ] لا توجد أخطاء في الـ logs

## 📞 الدعم

إذا استمرت المشكلة، تحقق من:
1. **logs النظام**: `storage/logs/laravel.log`
2. **console المتصفح**: F12 → Console
3. **قاعدة البيانات**: تأكد من وجود الصلاحيات والأدوار
