# تعليمات اختبار صفحة التسعير

## 🧪 خطوات الاختبار

### 1. اختبار الوصول للصفحة
```
URL: /pricing
```
- تسجيل الدخول كمستخدم لديه صلاحية `manage product & service`
- الانتقال إلى **إدارة العمليات المالية** > **التسعير**
- التأكد من ظهور الصفحة بدون أخطاء

### 2. اختبار عرض البيانات
- التأكد من ظهور جميع المنتجات في الجدول
- التأكد من ظهور الصور (أو أيقونة بديلة)
- التأكد من عرض جميع الحقول بشكل صحيح

### 3. اختبار البحث والفلترة
- البحث بالاسم: أدخل اسم منتج موجود
- البحث بـ SKU: أدخل رمز منتج موجود
- فلترة بالفئة: اختر فئة معينة
- فلترة بالنوع: اختر منتج أو خدمة

### 4. اختبار التعديل المباشر

#### أ. الحقول النصية (Name, SKU)
1. انقر على خلية الاسم
2. يجب أن تتحول إلى input field
3. غير النص واضغط Enter
4. يجب أن يحفظ التغيير ويظهر رسالة نجاح

#### ب. الحقول الرقمية (Sale Price, Purchase Price)
1. انقر على خلية السعر
2. يجب أن تتحول إلى number input
3. أدخل رقم جديد واضغط Enter
4. يجب أن يحفظ ويظهر بالتنسيق الصحيح

#### ج. القوائم المنسدلة (Category, Unit, Type, etc.)
1. انقر على خلية الفئة
2. يجب أن تظهر قائمة منسدلة
3. اختر قيمة جديدة
4. يجب أن تحفظ تلقائياً

### 5. اختبار معالجة الأخطاء
- محاولة إدخال نص في حقل رقمي
- محاولة إدخال SKU مكرر
- محاولة ترك حقل مطلوب فارغ

## 🔧 استكشاف الأخطاء

### خطأ 500 - Internal Server Error
**الأسباب المحتملة:**
1. Controller غير موجود أو غير مستورد
2. View غير موجود
3. خطأ في قاعدة البيانات

**الحلول:**
```bash
# تحقق من وجود الملفات
ls -la app/Http/Controllers/PricingController.php
ls -la resources/views/pricing/index.blade.php

# تحقق من الـ routes
php artisan route:list | grep pricing

# تحقق من الـ logs
tail -f storage/logs/laravel.log
```

### خطأ 404 - Page Not Found
**السبب:** Routes غير محدثة
**الحل:** تأكد من إضافة المسارات في `routes/web.php`

### خطأ 403 - Permission Denied
**السبب:** المستخدم لا يملك الصلاحيات
**الحل:** إعطاء صلاحية `manage product & service` للمستخدم

### التعديل المباشر لا يعمل
**الأسباب المحتملة:**
1. JavaScript غير محمل
2. CSRF token غير صحيح
3. مسارات AJAX غير صحيحة

**الحلول:**
```javascript
// تحقق من console المتصفح
console.log('jQuery loaded:', typeof jQuery !== 'undefined');
console.log('CSRF token:', $('meta[name="csrf-token"]').attr('content'));
```

## 📊 نتائج الاختبار المتوقعة

### ✅ النجاح
- الصفحة تحمل بدون أخطاء
- البيانات تظهر بشكل صحيح
- البحث والفلترة يعملان
- التعديل المباشر يعمل لجميع الحقول
- رسائل النجاح والخطأ تظهر بوضوح

### ❌ الفشل
- خطأ 500 أو 404
- البيانات لا تظهر
- التعديل المباشر لا يعمل
- رسائل خطأ غير واضحة

## 🛠️ أدوات التشخيص

### 1. فحص الـ Routes
```bash
php artisan route:list | grep pricing
```

### 2. فحص الـ Logs
```bash
tail -f storage/logs/laravel.log
```

### 3. فحص قاعدة البيانات
```sql
SELECT COUNT(*) FROM product_services;
SELECT COUNT(*) FROM product_service_categories;
SELECT COUNT(*) FROM product_service_units;
```

### 4. فحص الصلاحيات
```sql
SELECT name FROM permissions WHERE name LIKE '%product%';
```

## 📝 تقرير الاختبار

### معلومات النظام
- Laravel Version: ___
- PHP Version: ___
- Database: ___

### نتائج الاختبار
- [ ] الوصول للصفحة
- [ ] عرض البيانات
- [ ] البحث والفلترة
- [ ] التعديل المباشر - النصوص
- [ ] التعديل المباشر - الأرقام
- [ ] التعديل المباشر - القوائم
- [ ] معالجة الأخطاء

### ملاحظات
```
اكتب هنا أي ملاحظات أو مشاكل واجهتها أثناء الاختبار
```

### التوصيات
```
اكتب هنا أي تحسينات مقترحة
```
