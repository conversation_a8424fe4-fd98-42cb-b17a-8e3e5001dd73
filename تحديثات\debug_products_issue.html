<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار تحميل المنتجات</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        button { padding: 10px 15px; margin: 5px; }
        input, select { padding: 8px; margin: 5px; width: 200px; }
        #results { margin-top: 20px; padding: 10px; background: #f5f5f5; }
    </style>
</head>
<body>
    <h1>اختبار تحميل المنتجات - نظام أوامر الاستلام</h1>
    
    <div class="test-section">
        <h2>1. اختبار المسارات</h2>
        <button onclick="testRoutes()">اختبار المسارات</button>
        <div id="routes-result"></div>
    </div>

    <div class="test-section">
        <h2>2. اختبار تحميل المنتجات بالمستودع</h2>
        <label>معرف المستودع:</label>
        <input type="number" id="warehouse-id" value="1" placeholder="أدخل معرف المستودع">
        <button onclick="testLoadProducts()">تحميل المنتجات</button>
        <div id="load-result"></div>
    </div>

    <div class="test-section">
        <h2>3. اختبار البحث في المنتجات</h2>
        <label>نص البحث:</label>
        <input type="text" id="search-text" placeholder="اكتب اسم أو SKU المنتج">
        <label>معرف المستودع:</label>
        <input type="number" id="search-warehouse-id" value="1">
        <button onclick="testSearchProducts()">بحث</button>
        <div id="search-result"></div>
    </div>

    <div class="test-section">
        <h2>4. اختبار قاعدة البيانات</h2>
        <button onclick="testDatabase()">فحص قاعدة البيانات</button>
        <div id="db-result"></div>
    </div>

    <div class="test-section">
        <h2>5. اختبار الصلاحيات</h2>
        <button onclick="testPermissions()">فحص الصلاحيات</button>
        <div id="permissions-result"></div>
    </div>

    <div id="results">
        <h3>نتائج الاختبارات:</h3>
        <div id="all-results"></div>
    </div>

    <script>
        // تحديد URL الأساسي للموقع
        const BASE_URL = window.location.origin;
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const color = type === 'success' ? 'green' : type === 'error' ? 'red' : 'blue';
            const logEntry = `<div style="color: ${color};">[${timestamp}] ${message}</div>`;
            document.getElementById('all-results').innerHTML += logEntry;
            console.log(`[${timestamp}] ${message}`);
        }

        function testRoutes() {
            log('🔍 اختبار المسارات...', 'info');
            
            const routes = [
                '/receipt-order',
                '/receipt-order/create',
                '/receipt-order-warehouse-products',
                '/receipt-order-search-products'
            ];

            routes.forEach(route => {
                fetch(BASE_URL + route, {
                    method: 'HEAD',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => {
                    if (response.ok) {
                        log(`✅ المسار ${route} متاح`, 'success');
                        document.getElementById('routes-result').innerHTML += `<div class="success">✅ ${route}</div>`;
                    } else {
                        log(`❌ المسار ${route} غير متاح - كود: ${response.status}`, 'error');
                        document.getElementById('routes-result').innerHTML += `<div class="error">❌ ${route} - ${response.status}</div>`;
                    }
                })
                .catch(error => {
                    log(`❌ خطأ في المسار ${route}: ${error.message}`, 'error');
                    document.getElementById('routes-result').innerHTML += `<div class="error">❌ ${route} - خطأ</div>`;
                });
            });
        }

        function testLoadProducts() {
            const warehouseId = document.getElementById('warehouse-id').value;
            
            if (!warehouseId) {
                log('❌ يجب إدخال معرف المستودع', 'error');
                return;
            }

            log(`🔍 اختبار تحميل منتجات المستودع ${warehouseId}...`, 'info');

            $.ajax({
                url: BASE_URL + '/receipt-order-warehouse-products',
                method: 'GET',
                data: { warehouse_id: warehouseId },
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                },
                success: function(response) {
                    log('✅ نجح تحميل المنتجات', 'success');
                    log(`📊 البيانات المستلمة: ${JSON.stringify(response)}`, 'info');
                    
                    const resultDiv = document.getElementById('load-result');
                    if (response.success && response.products) {
                        resultDiv.innerHTML = `
                            <div class="success">✅ تم تحميل ${response.products.length} منتج</div>
                            <details>
                                <summary>عرض التفاصيل</summary>
                                <pre>${JSON.stringify(response, null, 2)}</pre>
                            </details>
                        `;
                    } else {
                        resultDiv.innerHTML = `<div class="error">❌ لا توجد منتجات أو خطأ في الاستجابة</div>`;
                    }
                },
                error: function(xhr, status, error) {
                    log(`❌ فشل تحميل المنتجات: ${error}`, 'error');
                    log(`📊 تفاصيل الخطأ: ${xhr.responseText}`, 'error');
                    
                    document.getElementById('load-result').innerHTML = `
                        <div class="error">❌ فشل التحميل: ${error}</div>
                        <details>
                            <summary>تفاصيل الخطأ</summary>
                            <pre>${xhr.responseText}</pre>
                        </details>
                    `;
                }
            });
        }

        function testSearchProducts() {
            const searchText = document.getElementById('search-text').value;
            const warehouseId = document.getElementById('search-warehouse-id').value;
            
            if (!searchText) {
                log('❌ يجب إدخال نص البحث', 'error');
                return;
            }

            log(`🔍 اختبار البحث عن "${searchText}" في المستودع ${warehouseId}...`, 'info');

            $.ajax({
                url: BASE_URL + '/receipt-order-search-products',
                method: 'GET',
                data: { 
                    search: searchText,
                    warehouse_id: warehouseId 
                },
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                },
                success: function(response) {
                    log('✅ نجح البحث', 'success');
                    log(`📊 نتائج البحث: ${JSON.stringify(response)}`, 'info');
                    
                    const resultDiv = document.getElementById('search-result');
                    if (response.success && response.products) {
                        resultDiv.innerHTML = `
                            <div class="success">✅ تم العثور على ${response.products.length} منتج</div>
                            <details>
                                <summary>عرض النتائج</summary>
                                <pre>${JSON.stringify(response, null, 2)}</pre>
                            </details>
                        `;
                    } else {
                        resultDiv.innerHTML = `<div class="error">❌ لم يتم العثور على منتجات</div>`;
                    }
                },
                error: function(xhr, status, error) {
                    log(`❌ فشل البحث: ${error}`, 'error');
                    log(`📊 تفاصيل الخطأ: ${xhr.responseText}`, 'error');
                    
                    document.getElementById('search-result').innerHTML = `
                        <div class="error">❌ فشل البحث: ${error}</div>
                        <details>
                            <summary>تفاصيل الخطأ</summary>
                            <pre>${xhr.responseText}</pre>
                        </details>
                    `;
                }
            });
        }

        function testDatabase() {
            log('🔍 اختبار قاعدة البيانات...', 'info');
            
            // اختبار وجود الجداول المطلوبة
            const tables = [
                'product_services',
                'warehouses', 
                'warehouse_products',
                'venders'
            ];

            document.getElementById('db-result').innerHTML = `
                <div class="info">📋 الجداول المطلوبة:</div>
                ${tables.map(table => `<div>• ${table}</div>`).join('')}
                <div class="info">💡 تحقق من وجود هذه الجداول في قاعدة البيانات</div>
            `;
        }

        function testPermissions() {
            log('🔍 اختبار الصلاحيات...', 'info');
            
            document.getElementById('permissions-result').innerHTML = `
                <div class="info">🔐 الصلاحيات المطلوبة:</div>
                <div>• manage warehouse</div>
                <div>• show warehouse</div>
                <div class="info">💡 تحقق من أن المستخدم الحالي لديه هذه الصلاحيات</div>
            `;
        }

        // تشغيل اختبار أولي عند تحميل الصفحة
        window.onload = function() {
            log('🚀 بدء اختبارات النظام...', 'info');
            log(`🌐 الموقع: ${BASE_URL}`, 'info');
        };
    </script>
</body>
</html>
