<?php
// تحليل محتوى صفحة شجرة الحسابات لمعرفة سبب عدم ظهور الجدول
echo "<h1>تحليل محتوى صفحة شجرة الحسابات</h1>";

// إعدادات قاعدة البيانات
$host = '127.0.0.1';
$dbname = 'ty';
$username = 'root';
$password = '';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch(PDOException $e) {
    echo "<p style='color: red;'>✗ خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage() . "</p>";
    exit;
}

echo "<h2>🔍 تحليل منطق العرض (View Logic)</h2>";

echo "<div style='background-color: #f8f9fa; padding: 15px; border-left: 4px solid #007bff;'>";
echo "<h3>الكود المسؤول عن عرض الجدول:</h3>";
echo "<pre style='background-color: #f1f1f1; padding: 10px; border-radius: 4px;'>";
echo htmlspecialchars('@foreach ($chartAccounts as $type => $accounts)
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h6>{{ $type }}</h6>
            </div>
            <div class="card-body table-border-style">
                <div class="table-responsive">
                    <table class="table">
                        <!-- رؤوس الجدول -->
                        <tbody>
                            @foreach ($accounts as $account)
                                <!-- صفوف البيانات -->
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
@endforeach');
echo "</pre>";
echo "</div>";

echo "<h2>🔍 تحليل منطق الكنترولر</h2>";

echo "<div style='background-color: #fff3cd; padding: 15px; border: 1px solid #ffeaa7; border-radius: 5px;'>";
echo "<h3>خطوات الكنترولر:</h3>";
echo "<ol>";
echo "<li><strong>فحص الصلاحية:</strong> <code>\Auth::user()->can('manage chart of account')</code></li>";
echo "<li><strong>جلب أنواع الحسابات:</strong> <code>ChartOfAccountType::where('created_by', '=', \Auth::user()->creatorId())->get()</code></li>";
echo "<li><strong>جلب الحسابات:</strong> <code>ChartOfAccount::whereIn('type', \$types->pluck('id'))->where('created_by', '=', \Auth::user()->creatorId())</code></li>";
echo "<li><strong>تجميع البيانات:</strong> تجميع الحسابات حسب النوع في <code>\$chartAccounts</code></li>";
echo "<li><strong>إرسال للعرض:</strong> <code>compact('chartAccounts', 'types', 'filter')</code></li>";
echo "</ol>";
echo "</div>";

echo "<h2>🔍 محاكاة منطق الكنترولر</h2>";

// محاكاة منطق الكنترولر
try {
    // الحصول على المستخدمين
    $stmt = $pdo->query("SELECT id, name, created_by FROM users");
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($users as $user) {
        $creatorId = $user['created_by'] ?? $user['id'];
        
        echo "<div style='border: 2px solid #dee2e6; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
        echo "<h3>👤 للمستخدم: {$user['name']} (Creator ID: $creatorId)</h3>";
        
        // الخطوة 1: جلب أنواع الحسابات
        $stmt = $pdo->prepare("SELECT * FROM chart_of_account_types WHERE created_by = ?");
        $stmt->execute([$creatorId]);
        $types = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<p><strong>1️⃣ أنواع الحسابات:</strong> " . count($types) . " نوع</p>";
        
        if (count($types) == 0) {
            echo "<p style='color: red; font-weight: bold;'>❌ لا توجد أنواع حسابات → لن يظهر أي جدول</p>";
            echo "<p><strong>السبب:</strong> الحلقة <code>@foreach (\$chartAccounts as \$type => \$accounts)</code> لن تعمل لأن <code>\$chartAccounts</code> فارغ</p>";
        } else {
            echo "<div style='margin-left: 20px;'>";
            foreach ($types as $type) {
                echo "<p>📁 {$type['name']} (ID: {$type['id']})</p>";
            }
            echo "</div>";
            
            // الخطوة 2: جلب الحسابات
            $typeIds = array_column($types, 'id');
            $placeholders = str_repeat('?,', count($typeIds) - 1) . '?';
            $stmt = $pdo->prepare("SELECT * FROM chart_of_accounts WHERE type IN ($placeholders) AND created_by = ?");
            $stmt->execute(array_merge($typeIds, [$creatorId]));
            $accounts = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo "<p><strong>2️⃣ الحسابات:</strong> " . count($accounts) . " حساب</p>";
            
            if (count($accounts) == 0) {
                echo "<p style='color: red; font-weight: bold;'>❌ لا توجد حسابات → ستظهر الجداول فارغة</p>";
                echo "<p><strong>السبب:</strong> ستظهر رؤوس الجداول لكن بدون صفوف بيانات</p>";
            } else {
                // الخطوة 3: تجميع الحسابات حسب النوع
                $chartAccounts = [];
                foreach ($types as $type) {
                    $typeName = $type['name'];
                    $typeAccounts = array_filter($accounts, function($account) use ($type) {
                        return $account['type'] == $type['id'];
                    });
                    $chartAccounts[$typeName] = $typeAccounts;
                    
                    if (count($typeAccounts) > 0) {
                        echo "<p style='color: green;'>✅ النوع '$typeName': " . count($typeAccounts) . " حساب</p>";
                        echo "<div style='margin-left: 20px;'>";
                        foreach ($typeAccounts as $account) {
                            echo "<p>💰 {$account['name']} (كود: {$account['code']})</p>";
                        }
                        echo "</div>";
                    } else {
                        echo "<p style='color: orange;'>⚠️ النوع '$typeName': 0 حساب (جدول فارغ)</p>";
                    }
                }
                
                $totalAccounts = array_sum(array_map('count', $chartAccounts));
                
                if ($totalAccounts > 0) {
                    echo "<p style='color: green; font-weight: bold; font-size: 16px;'>✅ النتيجة: ستظهر الجداول مع البيانات!</p>";
                    echo "<p style='color: green;'>إجمالي الحسابات: $totalAccounts</p>";
                } else {
                    echo "<p style='color: red; font-weight: bold;'>❌ النتيجة: ستظهر الجداول فارغة</p>";
                }
            }
        }
        
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>خطأ في المحاكاة: " . $e->getMessage() . "</p>";
}

echo "<h2>🔍 أسباب عدم ظهور الجدول</h2>";

echo "<div style='background-color: #f8d7da; padding: 15px; border: 1px solid #f5c6cb; border-radius: 5px;'>";
echo "<h3>الأسباب المحتملة:</h3>";
echo "<ol>";
echo "<li><strong>عدم وجود أنواع حسابات:</strong> إذا كان <code>\$types</code> فارغ، فلن تعمل الحلقة الخارجية</li>";
echo "<li><strong>عدم وجود حسابات:</strong> إذا كان <code>\$accounts</code> فارغ، ستظهر الجداول بدون بيانات</li>";
echo "<li><strong>عدم تطابق created_by:</strong> البيانات موجودة لكن لمستخدم آخر</li>";
echo "<li><strong>عدم وجود صلاحيات:</strong> المستخدم لا يملك صلاحية 'manage chart of account'</li>";
echo "<li><strong>عدم تسجيل الدخول:</strong> المستخدم غير مسجل دخول</li>";
echo "</ol>";
echo "</div>";

echo "<h2>🔧 الحلول</h2>";

echo "<div style='background-color: #d4edda; padding: 15px; border: 1px solid #c3e6cb; border-radius: 5px;'>";
echo "<h3>خطوات الحل:</h3>";
echo "<ol>";
echo "<li><strong>إنشاء البيانات الأساسية:</strong>";
echo "<br><a href='seed_chart_accounts_direct.php' style='background-color: #28a745; color: white; padding: 8px 15px; text-decoration: none; border-radius: 4px; margin: 5px 0; display: inline-block;'>إنشاء البيانات</a></li>";

echo "<li><strong>إصلاح قيم created_by:</strong>";
echo "<br><a href='fix_created_by_values.php' style='background-color: #007bff; color: white; padding: 8px 15px; text-decoration: none; border-radius: 4px; margin: 5px 0; display: inline-block;'>إصلاح created_by</a></li>";

echo "<li><strong>فحص الصلاحيات والمصادقة:</strong>";
echo "<br><a href='check_auth_permissions.php' style='background-color: #fd7e14; color: white; padding: 8px 15px; text-decoration: none; border-radius: 4px; margin: 5px 0; display: inline-block;'>فحص المصادقة</a></li>";

echo "<li><strong>اختبار الصفحة:</strong>";
echo "<br><a href='http://localhost/chart-of-account' target='_blank' style='background-color: #6f42c1; color: white; padding: 8px 15px; text-decoration: none; border-radius: 4px; margin: 5px 0; display: inline-block;'>فتح الصفحة</a></li>";
echo "</ol>";
echo "</div>";

echo "<h2>🔍 فحص سريع للحالة الحالية</h2>";
echo "<p><a href='quick_chart_check.php' style='background-color: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; font-size: 16px;'>🔍 فحص سريع للمشكلة</a></p>";
?>
