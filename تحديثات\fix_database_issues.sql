-- ملف إصلا<PERSON> شامل لمشاكل قاعدة البيانات
-- تشغيل هذه الأوامر في phpMyAdmin أو MySQL Workbench

-- 1. فحص وإصلاح جدول product_services
-- التحقق من وجود الجدول
SELECT 'Checking product_services table...' as status;

-- إن<PERSON><PERSON><PERSON> الجدول إذا لم يكن موجوداً
CREATE TABLE IF NOT EXISTS `product_services` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `sku` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `sale_price` decimal(16,2) NOT NULL DEFAULT '0.00',
  `purchase_price` decimal(16,2) NOT NULL DEFAULT '0.00',
  `quantity` float NOT NULL DEFAULT '0',
  `tax_id` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `category_id` int(11) NOT NULL DEFAULT '0',
  `unit_id` int(11) NOT NULL DEFAULT '0',
  `type` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `sale_chartaccount_id` int(11) NOT NULL DEFAULT '0',
  `expense_chartaccount_id` int(11) NOT NULL DEFAULT '0',
  `description` text COLLATE utf8mb4_unicode_ci,
  `pro_image` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_by` int(11) NOT NULL DEFAULT '0',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `expiry_date` date DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `product_services_created_by_index` (`created_by`),
  KEY `product_services_category_id_index` (`category_id`),
  KEY `product_services_unit_id_index` (`unit_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 2. فحص وإصلاح جدول product_service_categories
CREATE TABLE IF NOT EXISTS `product_service_categories` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `type` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '0',
  `chart_account_id` int(11) NOT NULL DEFAULT '0',
  `color` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '#fc544b',
  `show_in_pos` tinyint(1) NOT NULL DEFAULT '1',
  `warehouse_ids` json DEFAULT NULL,
  `created_by` int(11) NOT NULL DEFAULT '0',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 3. فحص وإصلاح جدول product_service_units
CREATE TABLE IF NOT EXISTS `product_service_units` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_by` int(11) NOT NULL DEFAULT '0',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 4. فحص وإصلاح جدول chart_of_account_types
CREATE TABLE IF NOT EXISTS `chart_of_account_types` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_by` int(11) NOT NULL DEFAULT '0',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 5. فحص وإصلاح جدول chart_of_accounts
CREATE TABLE IF NOT EXISTS `chart_of_accounts` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `code` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `type` int(11) NOT NULL DEFAULT '0',
  `parent` int(11) NOT NULL DEFAULT '0',
  `created_by` int(11) NOT NULL DEFAULT '0',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 6. إدراج البيانات الأساسية
-- أنواع الحسابات
INSERT IGNORE INTO `chart_of_account_types` (`name`, `created_by`, `created_at`, `updated_at`) VALUES
('Assets', 1, NOW(), NOW()),
('Liabilities', 1, NOW(), NOW()),
('Equity', 1, NOW(), NOW()),
('Income', 1, NOW(), NOW()),
('Expenses', 1, NOW(), NOW()),
('Costs of Goods Sold', 1, NOW(), NOW());

-- الحسابات الأساسية
INSERT IGNORE INTO `chart_of_accounts` (`name`, `code`, `type`, `parent`, `created_by`, `created_at`, `updated_at`)
SELECT 'مبيعات عامة', '4001', id, 0, 1, NOW(), NOW()
FROM `chart_of_account_types` WHERE `name` = 'Income' LIMIT 1;

INSERT IGNORE INTO `chart_of_accounts` (`name`, `code`, `type`, `parent`, `created_by`, `created_at`, `updated_at`)
SELECT 'تكلفة البضاعة المباعة', '5001', id, 0, 1, NOW(), NOW()
FROM `chart_of_account_types` WHERE `name` = 'Costs of Goods Sold' LIMIT 1;

-- فئات المنتجات
INSERT IGNORE INTO `product_service_categories` (`name`, `type`, `color`, `created_by`, `created_at`, `updated_at`) VALUES
('فئة عامة', 'product & service', '#fc544b', 1, NOW(), NOW()),
('إلكترونيات', 'product & service', '#28a745', 1, NOW(), NOW()),
('ملابس', 'product & service', '#007bff', 1, NOW(), NOW());

-- وحدات القياس
INSERT IGNORE INTO `product_service_units` (`name`, `created_by`, `created_at`, `updated_at`) VALUES
('قطعة', 1, NOW(), NOW()),
('كيلو', 1, NOW(), NOW()),
('متر', 1, NOW(), NOW()),
('لتر', 1, NOW(), NOW()),
('علبة', 1, NOW(), NOW()),
('كرتون', 1, NOW(), NOW());

-- 7. إصلاح الصلاحيات
-- التحقق من وجود جدول permissions
CREATE TABLE IF NOT EXISTS `permissions` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `guard_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `permissions_name_guard_name_unique` (`name`,`guard_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إضافة الصلاحيات
INSERT IGNORE INTO `permissions` (`name`, `guard_name`, `created_at`, `updated_at`) VALUES
('manage product & service', 'web', NOW(), NOW()),
('create product & service', 'web', NOW(), NOW()),
('edit product & service', 'web', NOW(), NOW()),
('delete product & service', 'web', NOW(), NOW()),
('show product & service', 'web', NOW(), NOW());

-- 8. فحص وإصلاح جدول roles
CREATE TABLE IF NOT EXISTS `roles` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `guard_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `roles_name_guard_name_unique` (`name`,`guard_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إضافة الأدوار الأساسية
INSERT IGNORE INTO `roles` (`name`, `guard_name`, `created_at`, `updated_at`) VALUES
('super admin', 'web', NOW(), NOW()),
('company', 'web', NOW(), NOW()),
('accountant', 'web', NOW(), NOW());

-- 9. جدول role_has_permissions
CREATE TABLE IF NOT EXISTS `role_has_permissions` (
  `permission_id` bigint(20) unsigned NOT NULL,
  `role_id` bigint(20) unsigned NOT NULL,
  PRIMARY KEY (`permission_id`,`role_id`),
  KEY `role_has_permissions_role_id_foreign` (`role_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ربط الصلاحيات بالأدوار
INSERT IGNORE INTO `role_has_permissions` (`permission_id`, `role_id`)
SELECT p.id, r.id 
FROM `permissions` p, `roles` r 
WHERE p.name IN ('manage product & service', 'create product & service', 'edit product & service', 'delete product & service', 'show product & service')
AND r.name IN ('super admin', 'company');

-- 10. فحص النتائج النهائية
SELECT 'Database repair completed!' as status;

SELECT 
    'Categories' as type, 
    COUNT(*) as count 
FROM `product_service_categories` 
WHERE `type` = 'product & service'

UNION ALL

SELECT 
    'Units' as type, 
    COUNT(*) as count 
FROM `product_service_units`

UNION ALL

SELECT 
    'Income Accounts' as type, 
    COUNT(*) as count 
FROM `chart_of_accounts` ca 
JOIN `chart_of_account_types` cat ON ca.type = cat.id 
WHERE cat.name = 'Income'

UNION ALL

SELECT 
    'Expense Accounts' as type, 
    COUNT(*) as count 
FROM `chart_of_accounts` ca 
JOIN `chart_of_account_types` cat ON ca.type = cat.id 
WHERE cat.name IN ('Expenses', 'Costs of Goods Sold')

UNION ALL

SELECT 
    'Permissions' as type, 
    COUNT(*) as count 
FROM `permissions` 
WHERE `name` LIKE '%product%service%';
