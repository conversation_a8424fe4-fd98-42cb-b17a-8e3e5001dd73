# قائمة فحص نشر نظام رصيد أول المدة

## ✅ الملفات المطلوب رفعها

### 1. Controllers
- [ ] `app/Http/Controllers/FinancialRecordController.php`

### 2. Models  
- [ ] `app/Models/Shift.php`
- [ ] `app/Models/FinancialRecord.php`
- [ ] `app/Models/DeliveryFinancialRecord.php`
- [ ] `app/Models/FinancialTransactions.php`

### 3. Services
- [ ] `app/Services/FinancialRecordService.php`
- [ ] `app/Services/FinancialTransactionService.php`

### 4. Views
- [ ] `resources/views/pos/financial_record/opening-balance.blade.php`
- [ ] `resources/views/pos/financial_record/index.blade.php`
- [ ] `resources/views/pos/financial_record/index_delivery.blade.php`

### 5. Routes
- [ ] إضافة routes الجديدة إلى `routes/web.php`

## ✅ قاعدة البيانات

### Migrations المطلوبة
- [ ] `create_shifts_table`
- [ ] `create_financial_records_table` 
- [ ] `create_delivery_financial_records_table`
- [ ] `create_financial_transactions_table`
- [ ] `add_is_sale_session_new_to_users_table`

### تشغيل Migrations
```bash
php artisan migrate
```

## ✅ الصلاحيات

### تأكد من وجود الصلاحيات التالية:
- [ ] `manage pos`
- [ ] `show financial record`

### إضافة الصلاحيات للمستخدمين:
```sql
-- تحديث صلاحيات المستخدم
UPDATE users SET is_sale_session_new = 1 WHERE id = [USER_ID];
```

## ✅ التشخيص

### 1. فحص الـ Logs
```bash
tail -f storage/logs/laravel.log
```

### 2. فحص Route للتشخيص
زيارة: `/debug-opening-balance`

### 3. فحص Network في المتصفح
- افتح Developer Tools
- تبويب Network
- ابحث عن طلب AJAX
- تحقق من الاستجابة

## ✅ الأخطاء الشائعة

### 1. View غير موجود
```
View [pos.financial_record.opening-balance] not found
```
**الحل:** تأكد من رفع ملف الـ view

### 2. Route غير موجود  
```
Route [pos.financial.opening.balance] not defined
```
**الحل:** تأكد من إضافة الـ routes

### 3. Class غير موجود
```
Class 'App\Services\FinancialRecordService' not found
```
**الحل:** تأكد من رفع ملفات الـ Services

### 4. Migration غير مطبق
```
Table 'shifts' doesn't exist
```
**الحل:** تشغيل `php artisan migrate`

### 5. صلاحيات مفقودة
```
Permission denied
```
**الحل:** تحديث صلاحيات المستخدم
