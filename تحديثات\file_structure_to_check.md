# هيكل ملفات Product Expiry System

## 📁 هيكل الملفات الكامل لـ Product Expiry

```
project_root/
│
├── app/
│   ├── Http/
│   │   └── Controllers/
│   │       ├── FinancialRecordController.php          ⭐ أهم ملف
│   │       └── BranchCashManagementController.php     🔧 مساعد
│   │
│   ├── Models/
│   │   ├── User.php                                   ⭐ مهم جداً
│   │   ├── Shift.php                                  🔧 مطلوب
│   │   ├── FinancialRecord.php                        🔧 مطلوب
│   │   ├── DeliveryFinancialRecord.php                🔧 مطلوب
│   │   └── FinancialTransactions.php                  🔧 مطلوب
│   │
│   └── Services/
│       ├── FinancialRecordService.php                 🔧 مطلوب
│       └── FinancialTransactionService.php            🔧 مطلوب
│
├── resources/
│   └── views/
│       ├── partials/
│       │   └── admin/
│       │       └── menu.blade.php                     ⭐ أهم ملف
│       │
│       ├── pos/
│       │   └── financial_record/
│       │       ├── opening-balance.blade.php          ⭐ أهم ملف
│       │       ├── index.blade.php                    🔧 مساعد
│       │       └── index_delivery.blade.php           🔧 مساعد
│       │
│       ├── voucher/
│       │   ├── payment/
│       │   │   └── show.blade.php                     📄 يستخدم المتغير
│       │   └── receipt/
│       │       └── show.blade.php                     📄 يستخدم المتغير
│       │
│       └── bankAccount/
│           └── financial_record/
│               └── index.blade.php                    📄 يستخدم المتغير
│
├── routes/
│   └── web.php                                        ⭐ مهم جداً
│
└── database/
    └── migrations/
        ├── 2025_03_09_184355_add_is_sale_session_new_to_users_table.php    ⭐ أساسي
        ├── 2025_03_08_192200_create_shifts_table.php                       🔧 مطلوب
        ├── 2025_03_09_131457_create_financial_records_table.php            🔧 مطلوب
        ├── 2025_03_24_152432_create_delivery_financial_records_table.php   🔧 مطلوب
        └── 2025_03_26_205047_create_financial_transactions_table.php       🔧 مطلوب
```

## 🎯 الملفات حسب الأولوية

### ⭐ أولوية عالية جداً (يجب فحصها أولاً)

#### 1. Controller الأساسي
```
app/Http/Controllers/FinancialRecordController.php
```

#### 2. View الأساسي
```
resources/views/pos/financial_record/opening-balance.blade.php
```

#### 3. القائمة الجانبية
```
resources/views/partials/admin/menu.blade.php
```

#### 4. Routes
```
routes/web.php
```

#### 5. Migration الأساسي
```
database/migrations/2025_03_09_184355_add_is_sale_session_new_to_users_table.php
```

#### 6. Model المستخدم
```
app/Models/User.php
```

### 🔧 أولوية عالية (فحص ثانوي)

#### 7. Models المساعدة
```
app/Models/Shift.php
app/Models/FinancialRecord.php
app/Models/DeliveryFinancialRecord.php
app/Models/FinancialTransactions.php
```

#### 8. Services
```
app/Services/FinancialRecordService.php
app/Services/FinancialTransactionService.php
```

#### 9. Migrations المساعدة
```
database/migrations/2025_03_08_192200_create_shifts_table.php
database/migrations/2025_03_09_131457_create_financial_records_table.php
database/migrations/2025_03_24_152432_create_delivery_financial_records_table.php
database/migrations/2025_03_26_205047_create_financial_transactions_table.php
```

### 📄 أولوية متوسطة (فحص اختياري)

#### 10. Views المساعدة
```
resources/views/pos/financial_record/index.blade.php
resources/views/pos/financial_record/index_delivery.blade.php
resources/views/voucher/payment/show.blade.php
resources/views/voucher/receipt/show.blade.php
resources/views/bankAccount/financial_record/index.blade.php
```

#### 11. Controller المساعد
```
app/Http/Controllers/BranchCashManagementController.php
```

## 🔍 أوامر الفحص السريع

### فحص وجود الملفات الأساسية
```bash
# الملفات الأهم
ls -la app/Http/Controllers/FinancialRecordController.php
ls -la resources/views/pos/financial_record/opening-balance.blade.php
ls -la resources/views/partials/admin/menu.blade.php

# فحص المجلدات
ls -la app/Models/
ls -la app/Services/
ls -la resources/views/pos/financial_record/
ls -la database/migrations/ | grep -E "(shift|financial|users)"
```

### فحص محتوى الملفات الحيوية
```bash
# فحص Controller
grep -n "opinningBalace" app/Http/Controllers/FinancialRecordController.php
grep -n "is_sale_session_new" app/Http/Controllers/FinancialRecordController.php

# فحص Menu
grep -n "is_sale_session_new" resources/views/partials/admin/menu.blade.php
grep -n "data-fmodel" resources/views/partials/admin/menu.blade.php

# فحص Routes
grep -n "pos.financial.opening.balance" routes/web.php
grep -n "FinancialRecordController" routes/web.php
```

## 📋 قائمة فحص مرحلية

### المرحلة 1: الملفات الأساسية
- [ ] `FinancialRecordController.php` موجود
- [ ] `opening-balance.blade.php` موجود
- [ ] `menu.blade.php` محدث
- [ ] `web.php` يحتوي على routes المطلوبة
- [ ] Migration `add_is_sale_session_new_to_users_table.php` موجود

### المرحلة 2: المحتوى
- [ ] Controller يحتوي على method `opinningBalace`
- [ ] View يحتوي على form صحيح
- [ ] Menu يحتوي على متغير `is_sale_session_new`
- [ ] Routes تشير للـ methods الصحيحة

### المرحلة 3: قاعدة البيانات
- [ ] حقل `is_sale_session_new` موجود في جدول users
- [ ] جدول `shifts` موجود
- [ ] جدول `financial_records` موجود

### المرحلة 4: الصلاحيات
- [ ] المستخدم لديه صلاحية `manage pos`
- [ ] المستخدم لديه صلاحية `show financial record`
- [ ] قيمة `is_sale_session_new = 1` للمستخدم

## 🚀 أوامر التشغيل بعد الفحص

```bash
# إذا كانت الملفات موجودة، شغل هذه الأوامر
php artisan migrate
php artisan config:cache
php artisan route:cache
php artisan view:cache
composer dump-autoload
```

## 📞 تقرير الفحص

بعد فحص الملفات، أرسل لي:

### ✅ الملفات الموجودة
- قائمة بالملفات الموجودة من القائمة أعلاه

### ❌ الملفات المفقودة
- قائمة بالملفات المفقودة

### 🔧 الملفات المختلفة
- أي ملفات موجودة لكن محتواها مختلف

### 🗄️ قاعدة البيانات
- نتيجة `DESCRIBE users;`
- نتيجة `SHOW TABLES LIKE 'shifts';`

### 🔐 الصلاحيات
- نتيجة فحص صلاحيات المستخدم

وسأساعدك في حل أي مشاكل محددة!
