-- سكريبت تشخيص سريع لمشكلة الفواتير غير المدفوعة
-- نفذ هذه الاستعلامات في phpMyAdmin أو أي أداة إدارة قاعدة بيانات

-- 1. فحص آخر 10 فواتير مع حالة الدفع
SELECT 
    p.id,
    p.pos_id,
    p.pos_date,
    p.customer_id,
    p.warehouse_id,
    pp.id as payment_id,
    pp.payment_type,
    pp.amount as payment_amount,
    CASE 
        WHEN pp.payment_type IS NOT NULL AND pp.payment_type != '' THEN 'مدفوع'
        ELSE 'غير مدفوع'
    END as status
FROM pos p 
LEFT JOIN pos_payments pp ON p.id = pp.pos_id 
ORDER BY p.id DESC 
LIMIT 10;

-- 2. إحصائيات عامة
SELECT 
    (SELECT COUNT(*) FROM pos) as total_invoices,
    (SELECT COUNT(*) FROM pos_payments) as total_payments,
    (SELECT COUNT(*) FROM pos p LEFT JOIN pos_payments pp ON p.id = pp.pos_id WHERE pp.id IS NULL) as missing_payments,
    (SELECT COUNT(*) FROM pos_payments WHERE payment_type IS NULL OR payment_type = '') as empty_payment_types;

-- 3. فحص الفواتير بدون سجلات دفع
SELECT 
    p.id,
    p.pos_id,
    p.pos_date,
    p.customer_id,
    p.warehouse_id,
    'لا يوجد سجل دفع' as issue
FROM pos p 
LEFT JOIN pos_payments pp ON p.id = pp.pos_id 
WHERE pp.id IS NULL
ORDER BY p.id DESC 
LIMIT 10;

-- 4. فحص سجلات الدفع مع payment_type فارغ
SELECT 
    pp.id,
    pp.pos_id,
    pp.amount,
    pp.payment_type,
    'payment_type فارغ' as issue
FROM pos_payments pp 
WHERE pp.payment_type IS NULL OR pp.payment_type = ''
ORDER BY pp.id DESC 
LIMIT 10;

-- 5. فحص آخر فاتورة تم إنشاؤها مع تفاصيلها
SELECT 
    p.*,
    pp.payment_type,
    pp.amount as payment_amount,
    pp.cash_amount,
    pp.network_amount,
    c.name as customer_name,
    w.name as warehouse_name
FROM pos p 
LEFT JOIN pos_payments pp ON p.id = pp.pos_id 
LEFT JOIN customers c ON p.customer_id = c.id
LEFT JOIN warehouses w ON p.warehouse_id = w.id
ORDER BY p.id DESC 
LIMIT 1;

-- 6. فحص منتجات آخر فاتورة
SELECT 
    pp.*,
    ps.name as product_name,
    ps.type as product_type
FROM pos_products pp 
LEFT JOIN product_services ps ON pp.product_id = ps.id
WHERE pp.pos_id = (SELECT id FROM pos ORDER BY id DESC LIMIT 1);

-- 7. فحص آخر سجلات stock_reports
SELECT 
    sr.*,
    ps.name as product_name,
    ps.type as product_type
FROM stock_reports sr 
LEFT JOIN product_services ps ON sr.product_id = ps.id
ORDER BY sr.id DESC 
LIMIT 10;

-- 8. فحص الفواتير التي تم إنشاؤها اليوم
SELECT 
    p.id,
    p.pos_id,
    p.pos_date,
    pp.payment_type,
    CASE 
        WHEN pp.payment_type IS NOT NULL AND pp.payment_type != '' THEN 'مدفوع'
        ELSE 'غير مدفوع'
    END as status
FROM pos p 
LEFT JOIN pos_payments pp ON p.id = pp.pos_id 
WHERE DATE(p.created_at) = CURDATE()
ORDER BY p.id DESC;

-- 9. فحص أنواع المنتجات في الفواتير غير المدفوعة
SELECT 
    ps.type as product_type,
    COUNT(*) as count
FROM pos p 
LEFT JOIN pos_payments pp ON p.id = pp.pos_id 
LEFT JOIN pos_products ppr ON p.id = ppr.pos_id
LEFT JOIN product_services ps ON ppr.product_id = ps.id
WHERE pp.id IS NULL
GROUP BY ps.type;

-- 10. فحص الورديات المفتوحة
SELECT 
    s.*,
    w.name as warehouse_name
FROM shifts s
LEFT JOIN warehouses w ON s.warehouse_id = w.id
WHERE s.closed_at IS NULL
ORDER BY s.id DESC;

-- ===== أوامر الإصلاح (نفذ فقط إذا لزم الأمر) =====

-- إصلاح 1: إنشاء سجلات دفع للفواتير المفقودة (تحذير: قم بعمل نسخة احتياطية أولاً!)
/*
INSERT INTO pos_payments (pos_id, date, amount, discount, payment_type, cash_amount, network_amount, created_by, created_at, updated_at)
SELECT 
    p.id as pos_id,
    p.pos_date as date,
    COALESCE((
        SELECT SUM(pp.price * pp.quantity) 
        FROM pos_products pp 
        WHERE pp.pos_id = p.id
    ), 0) as amount,
    0 as discount,
    'cash' as payment_type,
    COALESCE((
        SELECT SUM(pp.price * pp.quantity) 
        FROM pos_products pp 
        WHERE pp.pos_id = p.id
    ), 0) as cash_amount,
    0 as network_amount,
    p.created_by,
    p.created_at,
    p.updated_at
FROM pos p 
LEFT JOIN pos_payments pp ON p.id = pp.pos_id 
WHERE pp.id IS NULL;
*/

-- إصلاح 2: تحديث payment_type الفارغة
/*
UPDATE pos_payments 
SET payment_type = 'cash',
    cash_amount = amount,
    network_amount = 0
WHERE payment_type IS NULL OR payment_type = '';
*/
