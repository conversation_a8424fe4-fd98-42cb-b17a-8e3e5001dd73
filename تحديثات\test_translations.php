<?php
/**
 * ملف اختبار الترجمات العربية
 * Test Arabic Translations File
 * 
 * ضع هذا الملف في مجلد public واذهب إليه في المتصفح لاختبار الترجمات
 * Place this file in public folder and access it via browser to test translations
 */

// تحديد مسار Laravel
require_once __DIR__ . '/../vendor/autoload.php';

$app = require_once __DIR__ . '/../bootstrap/app.php';
$app->make('Illuminate\Contracts\Http\Kernel')->handle(
    Illuminate\Http\Request::capture()
);

// تعيين اللغة العربية
app()->setLocale('ar');

echo "<html dir='rtl' lang='ar'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<title>اختبار الترجمات العربية</title>";
echo "<style>";
echo "body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 20px; background: #f5f5f5; }";
echo ".container { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }";
echo ".test-item { margin: 10px 0; padding: 10px; border: 1px solid #ddd; border-radius: 4px; }";
echo ".success { background: #d4edda; border-color: #c3e6cb; color: #155724; }";
echo ".error { background: #f8d7da; border-color: #f5c6cb; color: #721c24; }";
echo ".info { background: #d1ecf1; border-color: #bee5eb; color: #0c5460; }";
echo "h1, h2 { color: #333; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<div class='container'>";
echo "<h1>🔍 اختبار الترجمات العربية</h1>";

// معلومات النظام
echo "<div class='test-item info'>";
echo "<h2>📊 معلومات النظام:</h2>";
echo "<p><strong>اللغة الحالية:</strong> " . app()->getLocale() . "</p>";
echo "<p><strong>مسار ملف الترجمة:</strong> " . resource_path('lang/ar.json') . "</p>";
echo "<p><strong>وجود الملف:</strong> " . (file_exists(resource_path('lang/ar.json')) ? '✅ موجود' : '❌ غير موجود') . "</p>";
echo "</div>";

// اختبار الترجمات المطلوبة
$translations_to_test = [
    'Branch Operations Management' => 'إدارة عمليات الفروع',
    'Financial Operations Management' => 'إدارة العمليات المالية', 
    'Company Messaging System' => 'نظام المراسلات بالشركة',
    'Dashboard' => 'لوحة القيادة',
    'Settings' => 'الإعدادات',
    'User' => 'المستخدم',
    'Reports' => 'التقارير'
];

echo "<div class='test-item'>";
echo "<h2>🧪 اختبار الترجمات:</h2>";

foreach ($translations_to_test as $english => $expected_arabic) {
    $translated = __($english);
    $is_translated = ($translated !== $english);
    
    echo "<div class='test-item " . ($is_translated ? 'success' : 'error') . "'>";
    echo "<strong>الإنجليزية:</strong> $english<br>";
    echo "<strong>المتوقع:</strong> $expected_arabic<br>";
    echo "<strong>النتيجة:</strong> $translated<br>";
    echo "<strong>الحالة:</strong> " . ($is_translated ? '✅ مترجم' : '❌ غير مترجم') . "<br>";
    echo "</div>";
}

echo "</div>";

// اختبار قراءة ملف الترجمة مباشرة
if (file_exists(resource_path('lang/ar.json'))) {
    echo "<div class='test-item info'>";
    echo "<h2>📄 محتوى ملف الترجمة (أول 10 عناصر):</h2>";
    
    $translations = json_decode(file_get_contents(resource_path('lang/ar.json')), true);
    
    if ($translations) {
        echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto;'>";
        $count = 0;
        foreach ($translations as $key => $value) {
            if ($count >= 10) break;
            echo htmlspecialchars("\"$key\": \"$value\"") . "\n";
            $count++;
        }
        echo "</pre>";
        echo "<p><strong>إجمالي الترجمات:</strong> " . count($translations) . "</p>";
    } else {
        echo "<p class='error'>❌ خطأ في قراءة ملف JSON</p>";
    }
    echo "</div>";
}

// تعليمات الحل
echo "<div class='test-item info'>";
echo "<h2>🛠️ خطوات الحل:</h2>";
echo "<ol>";
echo "<li>تأكد من أن اللغة مضبوطة على العربية في إعدادات النظام</li>";
echo "<li>امسح الكاش باستخدام: <code>php artisan cache:clear</code></li>";
echo "<li>امسح كاش التكوين: <code>php artisan config:clear</code></li>";
echo "<li>امسح كاش العروض: <code>php artisan view:clear</code></li>";
echo "<li>أعد تشغيل الخادم</li>";
echo "</ol>";
echo "</div>";

echo "</div>";
echo "</body>";
echo "</html>";
?>
