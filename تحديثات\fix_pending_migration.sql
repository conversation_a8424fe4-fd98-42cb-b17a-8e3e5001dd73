-- حل مشكلة الهجرة المعلقة
-- تشغيل هذا الملف سيحل المشكلة نهائياً

-- 1. حذف الجدول إذا كان موجود (احتياطي)
DROP TABLE IF EXISTS `receipt_order_products`;

-- 2. إن<PERSON><PERSON>ء الجدول بشكل صحيح
CREATE TABLE `receipt_order_products` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `receipt_order_id` bigint(20) UNSIGNED NOT NULL,
    `product_id` bigint(20) UNSIGNED NOT NULL,
    `quantity` decimal(15,2) NOT NULL,
    `unit_cost` decimal(15,2) NOT NULL DEFAULT 0.00,
    `total_cost` decimal(15,2) NOT NULL DEFAULT 0.00,
    `expiry_date` date DEFAULT NULL,
    `is_return` tinyint(1) NOT NULL DEFAULT 0,
    `notes` text DEFAULT NULL,
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `receipt_order_products_receipt_order_id_index` (`receipt_order_id`),
    KEY `receipt_order_products_product_id_index` (`product_id`),
    KEY `receipt_order_products_created_at_index` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 3. تحديث جدول migrations لتسجيل الهجرة كمكتملة
INSERT INTO `migrations` (`migration`, `batch`) 
VALUES ('2024_01_15_000002_create_receipt_order_products_table', 
        (SELECT COALESCE(MAX(batch), 0) + 1 FROM (SELECT batch FROM migrations) as temp));

-- 4. التحقق من نجاح العملية
SELECT 'تم إنشاء جدول receipt_order_products بنجاح!' as status;
SELECT COUNT(*) as total_migrations FROM migrations WHERE migration LIKE '%receipt%';
DESCRIBE receipt_order_products;
