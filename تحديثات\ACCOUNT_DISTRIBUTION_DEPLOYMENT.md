# 🚀 دليل نشر شاشة توزيع الحسابات

## 📁 الملفات المطلوب نقلها للخادم

### 1. ملفات جديدة (يجب إنشاؤها)
```
app/Http/Controllers/AccountDistributionController.php
resources/views/account-distribution/index.blade.php
```

### 2. ملفات محدثة (يجب استبدالها)
```
app/Models/ProductService.php
routes/web.php
resources/views/partials/admin/menu.blade.php
```

## 🔧 خطوات النشر

### الخطوة 1: نسخ الملفات
1. **رفع الملفات الجديدة:**
   - `app/Http/Controllers/AccountDistributionController.php`
   - `resources/views/account-distribution/index.blade.php`

2. **استبدال الملفات المحدثة:**
   - `app/Models/ProductService.php`
   - `routes/web.php`
   - `resources/views/partials/admin/menu.blade.php`

### الخطوة 2: تنظيف الكاش
```bash
php artisan cache:clear
php artisan route:clear
php artisan view:clear
php artisan config:clear
```

### الخطوة 3: التحقق من الصلاحيات
تأكد من أن المستخدمين لديهم صلاحية:
```
manage product & service
```

## 🎯 الوصول للميزة الجديدة

### المسار:
```
/account-distribution
```

### الموقع في القائمة:
```
إدارة العمليات المالية > توزيع الحسابات
```

### المستخدمون المخولون:
- مستخدمي نوع `company`
- مستخدمي نوع `accountant`

## ✅ اختبار ما بعد النشر

### 1. اختبار الوصول
- [ ] تسجيل الدخول كمستخدم company
- [ ] الذهاب إلى قسم إدارة العمليات المالية
- [ ] النقر على "توزيع الحسابات"
- [ ] التأكد من ظهور الصفحة بنجاح

### 2. اختبار الوظائف
- [ ] عرض قائمة المنتجات
- [ ] تحديث حساب الدخل لمنتج
- [ ] تحديث حساب الصرف لمنتج
- [ ] البحث في الجدول
- [ ] ترتيب الأعمدة

### 3. اختبار الأمان
- [ ] منع وصول المستخدمين غير المخولين
- [ ] التحقق من عمل CSRF Protection
- [ ] التأكد من عدم تعديل منتجات المستخدمين الآخرين

## 🐛 استكشاف الأخطاء

### خطأ 404 (الصفحة غير موجودة)
**السبب:** لم يتم تنظيف كاش المسارات
**الحل:**
```bash
php artisan route:clear
php artisan cache:clear
```

### خطأ 500 (خطأ في الخادم)
**الأسباب المحتملة:**
1. خطأ في ملف Controller
2. خطأ في ملف View
3. مشكلة في قاعدة البيانات

**الحل:**
1. فحص ملفات السجلات: `storage/logs/laravel.log`
2. التأكد من صحة الملفات المرفوعة
3. التحقق من اتصال قاعدة البيانات

### خطأ 403 (ممنوع الوصول)
**السبب:** المستخدم لا يملك الصلاحية المطلوبة
**الحل:** التأكد من وجود صلاحية `manage product & service`

### لا تظهر في القائمة
**الأسباب المحتملة:**
1. لم يتم تنظيف كاش العرض
2. المستخدم ليس من نوع company أو accountant

**الحل:**
```bash
php artisan view:clear
```

## 📊 قاعدة البيانات

### الجداول المستخدمة:
- `product_services` - المنتجات والخدمات
- `chart_of_accounts` - دليل الحسابات
- `product_service_categories` - فئات المنتجات

### الحقول المهمة:
- `product_services.sale_chartaccount_id` - حساب الدخل
- `product_services.expense_chartaccount_id` - حساب الصرف

## 🔒 الأمان

### الصلاحيات المطلوبة:
```php
Auth::user()->can('manage product & service')
```

### الحماية المطبقة:
- CSRF Protection
- التحقق من ملكية البيانات
- فلترة المدخلات
- التحقق من صحة البيانات

## 📈 المراقبة

### مؤشرات الأداء:
- عدد المنتجات المعروضة
- سرعة تحميل الصفحة
- معدل نجاح التحديثات

### السجلات:
- تسجيل جميع التحديثات
- تسجيل محاولات الوصول غير المصرح بها
- تسجيل الأخطاء

## 🆘 الدعم الفني

### في حالة وجود مشاكل:
1. فحص ملفات السجلات
2. التأكد من صحة الملفات المرفوعة
3. التحقق من الصلاحيات
4. تنظيف جميع أنواع الكاش
5. إعادة تشغيل الخادم إذا لزم الأمر

### معلومات مهمة للدعم:
- إصدار Laravel
- إصدار PHP
- نوع قاعدة البيانات
- رسالة الخطأ الكاملة
- خطوات إعادة إنتاج المشكلة

---
**ملاحظة:** تأكد من عمل نسخة احتياطية قبل النشر!
