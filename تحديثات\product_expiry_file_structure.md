# 📁 هيكل ملفات Product Expiry System الكامل

## 🎯 هيكل الملفات الأساسي

```
project_root/
│
├── app/
│   ├── Http/
│   │   └── Controllers/
│   │       └── ProductExpiryController.php                    ⭐ Controller الأساسي
│   │
│   └── Models/
│       ├── ProductExpiryDate.php                              ⭐ Model الأساسي
│       ├── ProductService.php                                 🔗 مرتبط (المنتجات)
│       ├── warehouse.php                                      🔗 مرتبط (المستودعات)
│       └── WarehouseProduct.php                               🔗 مرتبط (منتجات المستودع)
│
├── resources/
│   └── views/
│       ├── product_expiry/
│       │   ├── index.blade.php                                ⭐ الصفحة الرئيسية
│       │   ├── edit.blade.php                                 ⭐ صفحة التعديل
│       │   └── show.blade.php                                 ⭐ صفحة التفاصيل
│       │
│       └── partials/
│           └── admin/
│               └── menu.blade.php                             ⭐ القائمة الجانبية
│
├── routes/
│   └── web.php                                                ⭐ المسارات
│
└── database/
    └── migrations/
        ├── 2024_06_02_000001_create_product_expiry_dates_table.php    ⭐ جدول التواريخ
        └── 2024_06_01_000002_add_product_expiry_permissions.php       ⭐ الصلاحيات
```

## 🎮 الملفات حسب الأولوية

### ⭐ أولوية عالية جداً (أساسية)

#### 1. Controller الأساسي
```
app/Http/Controllers/ProductExpiryController.php
```
**المحتوى المطلوب:**
- `index()` - عرض قائمة المنتجات مع تواريخ الصلاحية
- `edit()` - عرض نموذج تعديل تاريخ الصلاحية
- `update()` - تحديث تاريخ الصلاحية
- `show()` - عرض تفاصيل المنتج مع تاريخ الصلاحية

#### 2. Model الأساسي
```
app/Models/ProductExpiryDate.php
```
**المحتوى المطلوب:**
- `$fillable` array
- `product()` relationship
- `warehouse()` relationship
- `getExpiryStatus()` method

#### 3. Views الأساسية
```
resources/views/product_expiry/index.blade.php
resources/views/product_expiry/edit.blade.php
resources/views/product_expiry/show.blade.php
```

#### 4. Routes
```
routes/web.php
```
**المسارات المطلوبة:**
```php
Route::get('product-expiry', [ProductExpiryController::class, 'index'])->name('product.expiry.index');
Route::get('product-expiry/{id}/edit', [ProductExpiryController::class, 'edit'])->name('product.expiry.edit');
Route::get('product-expiry/{id}/show', [ProductExpiryController::class, 'show'])->name('product.expiry.show');
Route::put('product-expiry/{id}', [ProductExpiryController::class, 'update'])->name('product.expiry.update');
```

#### 5. Database Migrations
```
database/migrations/2024_06_02_000001_create_product_expiry_dates_table.php
database/migrations/2024_06_01_000002_add_product_expiry_permissions.php
```

#### 6. القائمة الجانبية
```
resources/views/partials/admin/menu.blade.php
```
**المحتوى المطلوب:**
- روابط في قسم POS System للـ Cashier
- روابط في قسم Inventory Management للأدوار الأخرى

### 🔗 أولوية عالية (مرتبطة)

#### 7. Models المرتبطة
```
app/Models/ProductService.php
app/Models/warehouse.php
app/Models/WarehouseProduct.php
```

## 🔍 أوامر الفحص السريع

### فحص وجود الملفات الأساسية
```bash
# الملفات الأهم
ls -la app/Http/Controllers/ProductExpiryController.php
ls -la app/Models/ProductExpiryDate.php
ls -la resources/views/product_expiry/
ls -la database/migrations/ | grep -E "(product_expiry|expiry)"
```

### فحص محتوى الملفات الحيوية
```bash
# فحص Controller
grep -n "class ProductExpiryController" app/Http/Controllers/ProductExpiryController.php
grep -n "public function index" app/Http/Controllers/ProductExpiryController.php
grep -n "public function edit" app/Http/Controllers/ProductExpiryController.php
grep -n "public function update" app/Http/Controllers/ProductExpiryController.php
grep -n "public function show" app/Http/Controllers/ProductExpiryController.php

# فحص Model
grep -n "class ProductExpiryDate" app/Models/ProductExpiryDate.php
grep -n "fillable" app/Models/ProductExpiryDate.php

# فحص Routes
grep -n "product-expiry" routes/web.php
grep -n "ProductExpiryController" routes/web.php

# فحص Menu
grep -n "product.expiry" resources/views/partials/admin/menu.blade.php
```

### فحص قاعدة البيانات
```sql
-- فحص وجود الجدول
SHOW TABLES LIKE 'product_expiry_dates';

-- فحص هيكل الجدول
DESCRIBE product_expiry_dates;

-- فحص الصلاحيات
SELECT name FROM permissions WHERE name LIKE '%product expiry%';
```

## 📋 قائمة فحص مرحلية

### المرحلة 1: الملفات الأساسية
- [ ] `ProductExpiryController.php` موجود
- [ ] `ProductExpiryDate.php` موجود
- [ ] مجلد `resources/views/product_expiry/` موجود
- [ ] ملفات Views الثلاثة موجودة (index, edit, show)
- [ ] Migrations موجودة

### المرحلة 2: المحتوى
- [ ] Controller يحتوي على الـ methods الأربعة
- [ ] Model يحتوي على relationships
- [ ] Routes موجودة في web.php
- [ ] Menu يحتوي على روابط product expiry

### المرحلة 3: قاعدة البيانات
- [ ] جدول `product_expiry_dates` موجود
- [ ] صلاحيات `show product expiry` و `edit product expiry` موجودة
- [ ] الأدوار لديها الصلاحيات المطلوبة

### المرحلة 4: الصلاحيات
- [ ] المستخدم لديه صلاحية `show product expiry`
- [ ] المستخدم لديه صلاحية `edit product expiry`
- [ ] أو المستخدم لديه دور `Cashier`

## 🚀 أوامر التشغيل بعد الفحص

```bash
# إذا كانت الملفات موجودة، شغل هذه الأوامر
php artisan migrate
php artisan config:cache
php artisan route:cache
php artisan view:cache
composer dump-autoload
```

## 📞 تقرير الفحص المطلوب

بعد فحص الملفات، أرسل لي:

### ✅ الملفات الموجودة
- [ ] ProductExpiryController.php
- [ ] ProductExpiryDate.php
- [ ] Views (index, edit, show)
- [ ] Migrations

### ❌ الملفات المفقودة
- قائمة بأي ملفات مفقودة

### 🔧 الملفات المختلفة
- أي ملفات موجودة لكن محتواها مختلف

### 🗄️ قاعدة البيانات
- نتيجة `SHOW TABLES LIKE 'product_expiry_dates';`
- نتيجة `SELECT name FROM permissions WHERE name LIKE '%product expiry%';`

### 🔐 الصلاحيات
- نتيجة فحص صلاحيات المستخدم
