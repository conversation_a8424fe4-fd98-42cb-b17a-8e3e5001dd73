<?php
/**
 * ملف تشخيص مشاكل إنشاء المنتجات
 * يمكن تشغيل هذا الملف من خلال artisan command أو مباشرة لفحص المشاكل
 */

// تحقق من وجود الفئات والوحدات
echo "=== فحص البيانات الأساسية ===\n";

// فحص الفئات
$categories = DB::table('product_service_categories')
    ->where('type', 'product & service')
    ->get();

echo "عدد الفئات المتاحة: " . $categories->count() . "\n";
if ($categories->count() == 0) {
    echo "⚠️ لا توجد فئات منتجات! يجب إنشاء فئة واحدة على الأقل\n";
}

// فحص الوحدات
$units = DB::table('product_service_units')->get();
echo "عدد الوحدات المتاحة: " . $units->count() . "\n";
if ($units->count() == 0) {
    echo "⚠️ لا توجد وحدات! يجب إنشاء وحدة واحدة على الأقل\n";
}

// فحص حسابات الإيرادات
$incomeAccounts = DB::table('chart_of_accounts')
    ->join('chart_of_account_types', 'chart_of_accounts.type', '=', 'chart_of_account_types.id')
    ->where('chart_of_account_types.name', 'income')
    ->get();

echo "عدد حسابات الإيرادات: " . $incomeAccounts->count() . "\n";
if ($incomeAccounts->count() == 0) {
    echo "⚠️ لا توجد حسابات إيرادات! يجب إنشاء حساب إيرادات واحد على الأقل\n";
}

// فحص حسابات المصروفات
$expenseAccounts = DB::table('chart_of_accounts')
    ->join('chart_of_account_types', 'chart_of_accounts.type', '=', 'chart_of_account_types.id')
    ->whereIn('chart_of_account_types.name', ['Expenses', 'Costs of Goods Sold'])
    ->get();

echo "عدد حسابات المصروفات: " . $expenseAccounts->count() . "\n";
if ($expenseAccounts->count() == 0) {
    echo "⚠️ لا توجد حسابات مصروفات! يجب إنشاء حساب مصروفات واحد على الأقل\n";
}

echo "\n=== فحص الصلاحيات ===\n";

// فحص صلاحيات المنتجات
$permissions = DB::table('permissions')
    ->where('name', 'like', '%product%service%')
    ->get();

echo "صلاحيات المنتجات الموجودة:\n";
foreach ($permissions as $permission) {
    echo "- " . $permission->name . "\n";
}

echo "\n=== حلول المشاكل المحتملة ===\n";

if ($categories->count() == 0) {
    echo "1. إنشاء فئة منتجات:\n";
    echo "   INSERT INTO product_service_categories (name, type, created_by, created_at, updated_at) VALUES ('فئة عامة', 'product & service', 1, NOW(), NOW());\n\n";
}

if ($units->count() == 0) {
    echo "2. إنشاء وحدة قياس:\n";
    echo "   INSERT INTO product_service_units (name, created_by, created_at, updated_at) VALUES ('قطعة', 1, NOW(), NOW());\n\n";
}

if ($incomeAccounts->count() == 0) {
    echo "3. إنشاء حساب إيرادات:\n";
    echo "   يجب إنشاء حساب إيرادات من خلال إعدادات الحسابات\n\n";
}

if ($expenseAccounts->count() == 0) {
    echo "4. إنشاء حساب مصروفات:\n";
    echo "   يجب إنشاء حساب مصروفات من خلال إعدادات الحسابات\n\n";
}

echo "=== نصائح إضافية ===\n";
echo "1. تأكد من أن المستخدم لديه صلاحية 'create product & service'\n";
echo "2. تحقق من أن جميع الحقول المطلوبة مملوءة\n";
echo "3. تأكد من أن SKU فريد\n";
echo "4. تحقق من أن الأسعار أرقام صحيحة\n";
echo "5. راجع ملف logs/laravel.log للأخطاء التفصيلية\n";
