# دليل نشر صلاحية عرض جميع أوامر الاستلام للمستخدم Company

## 🎯 الهدف
إضافة صلاحية للمستخدم الذي لديه دور "company" لعرض جميع أوامر الاستلام الصادرة في النظام، وليس فقط الأوامر المرتبطة به.

## ✅ التحسينات المطبقة

### 📁 **الملفات المحدثة:**

#### **1. الكونترولر**
```
📁 app/Http/Controllers/ReceiptOrderController.php
```

**التغييرات:**

1. **دالة `index` - عرض جميع الأوامر للمستخدم company:**
   ```php
   // إذا كان المستخدم لديه دور company، أظهر جميع الأوامر
   if ($user->hasRole('company')) {
       // عرض جميع أوامر الاستلام في النظام
       $receiptOrdersQuery = $receiptOrdersQuery->whereNotNull('id');
   } else {
       // للمستخدمين الآخرين، أظهر أوامرهم فقط
       $receiptOrdersQuery = $receiptOrdersQuery->where(function($query) use ($user) {
           $query->where('created_by', $user->id)
                 ->orWhere('created_by', $user->creatorId());
       });
   }
   ```

2. **دالة `show` - عرض تفاصيل جميع الأوامر للمستخدم company:**
   ```php
   // إذا كان المستخدم لديه دور company، يمكنه عرض جميع الأوامر
   if (!$user->hasRole('company')) {
       // للمستخدمين الآخرين، تحقق من الصلاحية
       $receiptOrderQuery = $receiptOrderQuery->where(function($query) use ($user) {
           $query->where('created_by', $user->id)
                 ->orWhere('created_by', $user->creatorId());
       });
   }
   ```

3. **إضافة دور company للصلاحيات:**
   - تم إضافة `Auth::user()->hasRole('company')` لدالتي `index` و `show`

#### **2. القائمة الجانبية**
```
📁 resources/views/partials/admin/menu.blade.php
```

**التغيير:**
- إضافة عنصر "أوامر الاستلام" في قسم "إدارة العمليات المالية"
- العنصر يظهر للمستخدمين الذين لديهم دور company أو accountant

## 🚀 **خطوات النشر**

### **الخطوة 1: رفع الملفات المحدثة**
```bash
# رفع الكونترولر المحدث
scp app/Http/Controllers/ReceiptOrderController.php user@server:/path/to/project/app/Http/Controllers/

# رفع ملف القائمة الجانبية المحدث
scp resources/views/partials/admin/menu.blade.php user@server:/path/to/project/resources/views/partials/admin/
```

### **الخطوة 2: ضبط الصلاحيات**
```bash
# ضبط صلاحيات الملفات
ssh user@server "chmod 644 /path/to/project/app/Http/Controllers/ReceiptOrderController.php"
ssh user@server "chmod 644 /path/to/project/resources/views/partials/admin/menu.blade.php"
```

### **الخطوة 3: مسح الكاش**
```bash
# مسح كاش Laravel
ssh user@server "cd /path/to/project && php artisan cache:clear"
ssh user@server "cd /path/to/project && php artisan view:clear"
ssh user@server "cd /path/to/project && php artisan route:clear"
```

## 🔐 **الصلاحيات الجديدة**

### **للمستخدم الذي لديه دور company:**
- ✅ **عرض جميع أوامر الاستلام** في النظام (من جميع المستخدمين)
- ✅ **عرض تفاصيل جميع أوامر الاستلام**
- ✅ **الوصول من قسم إدارة العمليات المالية**
- ✅ **مراقبة شاملة** لجميع عمليات الاستلام في النظام

### **للمستخدمين الآخرين (Cashier، إلخ):**
- ✅ **عرض أوامرهم فقط** (كما هو الحال سابقاً)
- ✅ **عرض تفاصيل أوامرهم فقط**

## 🌐 **الوصول للنظام**

### **للمستخدم الذي لديه دور company:**
1. تسجيل الدخول للنظام
2. الذهاب إلى قسم **"إدارة العمليات المالية"** في القائمة الجانبية
3. النقر على **"أوامر الاستلام"**
4. عرض جميع أوامر الاستلام في النظام
5. النقر على أي أمر لعرض التفاصيل الكاملة

## 🧪 **للاختبار**

### **1. اختبار المستخدم company:**
```
✅ تسجيل الدخول بحساب لديه دور company
✅ التحقق من ظهور "أوامر الاستلام" في قسم إدارة العمليات المالية
✅ النقر على الرابط والتأكد من عمله
✅ التحقق من عرض جميع أوامر الاستلام (من جميع المستخدمين)
✅ النقر على أي أمر والتحقق من عرض التفاصيل
```

### **2. اختبار المستخدمين الآخرين:**
```
✅ تسجيل الدخول بحساب Cashier
✅ التحقق من عرض أوامره فقط (وليس جميع الأوامر)
✅ التأكد من عدم تأثر الوظائف الحالية
```

### **3. اختبار الصلاحيات:**
```
✅ التأكد من أن company يرى جميع الأوامر
✅ التأكد من أن Cashier يرى أوامره فقط
✅ التأكد من عمل عرض التفاصيل لكلا النوعين
```

## 📋 **ملاحظات مهمة**

1. **الشمولية:** المستخدم company يرى جميع أوامر الاستلام في النظام
2. **الأمان:** المستخدمون الآخرون يرون أوامرهم فقط
3. **التوافق:** التغييرات متوافقة مع الأدوار الأخرى الموجودة
4. **الأداء:** لا تؤثر التغييرات على أداء النظام
5. **المراقبة:** يمكن للمستخدم company مراقبة جميع عمليات الاستلام

## 🔄 **التراجع عن التغييرات (إذا لزم الأمر)**

### **إزالة الوصول لدور company:**
```bash
# التراجع عن تغييرات الكونترولر
# إزالة "|| Auth::user()->hasRole('company')" من الدالتين
# إزالة شرط if ($user->hasRole('company')) من دالة index
# إزالة شرط if (!$user->hasRole('company')) من دالة show

# التراجع عن تغييرات القائمة الجانبية
# إخفاء عنصر أوامر الاستلام من قسم إدارة العمليات المالية
```

## ✨ **المميزات الجديدة**

- 🎯 **مراقبة شاملة** لجميع أوامر الاستلام في النظام
- 📊 **رؤية إدارية** كاملة لعمليات الاستلام
- 🔍 **تتبع متقدم** لجميع العمليات
- 📈 **تحليل شامل** لأداء جميع المستخدمين
- 🛡️ **أمان محكم** مع الحفاظ على صلاحيات المستخدمين الآخرين
- 🎨 **تكامل سلس** مع قسم إدارة العمليات المالية
- ⚡ **أداء محسن** بدون تأثير على النظام

## 📞 **الدعم**

إذا واجهت أي مشاكل:
1. تحقق من سجلات الأخطاء في Laravel
2. تأكد من وجود دور "company" في النظام
3. تحقق من صحة بيانات المستخدمين في قاعدة البيانات
4. تأكد من مسح الكاش بشكل صحيح
