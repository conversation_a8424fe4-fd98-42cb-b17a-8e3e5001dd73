<?php
// فحص المصادقة والصلاحيات لصفحة شجرة الحسابات
echo "<h1>فحص المصادقة والصلاحيات</h1>";

// إعدادات قاعدة البيانات
$host = '127.0.0.1';
$dbname = 'ty';
$username = 'root';
$password = '';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<p style='color: green;'>✓ الاتصال بقاعدة البيانات ناجح</p>";
} catch(PDOException $e) {
    echo "<p style='color: red;'>✗ خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage() . "</p>";
    exit;
}

echo "<h2>1. فحص جلسة المستخدم</h2>";

// بدء الجلسة
session_start();

echo "<div style='background-color: #f8f9fa; padding: 15px; border-left: 4px solid #007bff;'>";
echo "<h3>معلومات الجلسة:</h3>";

if (isset($_SESSION['user_id'])) {
    echo "<p style='color: green;'>✓ المستخدم مسجل دخول</p>";
    echo "<p>معرف المستخدم: {$_SESSION['user_id']}</p>";
} else {
    echo "<p style='color: red;'>✗ المستخدم غير مسجل دخول</p>";
    echo "<p><strong>هذا قد يكون سبب المشكلة!</strong></p>";
}

echo "<p><strong>محتويات الجلسة:</strong></p>";
echo "<pre style='background-color: #f1f1f1; padding: 10px; border-radius: 4px;'>";
print_r($_SESSION);
echo "</pre>";
echo "</div>";

echo "<h2>2. فحص المستخدمين في قاعدة البيانات</h2>";

try {
    $stmt = $pdo->query("SELECT id, name, email, type, created_by FROM users");
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>ID</th><th>الاسم</th><th>البريد</th><th>النوع</th><th>Created By</th><th>حالة تسجيل الدخول</th></tr>";
    
    foreach ($users as $user) {
        $isLoggedIn = isset($_SESSION['user_id']) && $_SESSION['user_id'] == $user['id'];
        $loginStatus = $isLoggedIn ? "<span style='color: green;'>✓ مسجل دخول</span>" : "<span style='color: gray;'>غير مسجل</span>";
        
        echo "<tr" . ($isLoggedIn ? " style='background-color: #d4edda;'" : "") . ">";
        echo "<td>{$user['id']}</td>";
        echo "<td>{$user['name']}</td>";
        echo "<td>{$user['email']}</td>";
        echo "<td>{$user['type']}</td>";
        echo "<td>{$user['created_by']}</td>";
        echo "<td>$loginStatus</td>";
        echo "</tr>";
    }
    echo "</table>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>خطأ في فحص المستخدمين: " . $e->getMessage() . "</p>";
}

echo "<h2>3. فحص الصلاحيات</h2>";

try {
    // فحص جدول الصلاحيات
    $stmt = $pdo->query("SHOW TABLES LIKE 'permissions'");
    if ($stmt->rowCount() > 0) {
        echo "<p style='color: green;'>✓ جدول الصلاحيات موجود</p>";
        
        // البحث عن صلاحية شجرة الحسابات
        $stmt = $pdo->query("SELECT * FROM permissions WHERE name LIKE '%chart%account%' OR name LIKE '%manage%chart%'");
        $chartPermissions = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (count($chartPermissions) > 0) {
            echo "<h3>صلاحيات شجرة الحسابات:</h3>";
            echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
            echo "<tr><th>ID</th><th>الاسم</th><th>الوصف</th></tr>";
            foreach ($chartPermissions as $perm) {
                echo "<tr>";
                echo "<td>{$perm['id']}</td>";
                echo "<td>{$perm['name']}</td>";
                echo "<td>" . ($perm['display_name'] ?? 'غير محدد') . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p style='color: orange;'>⚠ لم يتم العثور على صلاحيات شجرة الحسابات</p>";
        }
        
    } else {
        echo "<p style='color: orange;'>⚠ جدول الصلاحيات غير موجود</p>";
    }
    
    // فحص جدول الأدوار
    $stmt = $pdo->query("SHOW TABLES LIKE 'roles'");
    if ($stmt->rowCount() > 0) {
        echo "<h3>الأدوار المتاحة:</h3>";
        $stmt = $pdo->query("SELECT * FROM roles LIMIT 5");
        $roles = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>الاسم</th><th>الوصف</th></tr>";
        foreach ($roles as $role) {
            echo "<tr>";
            echo "<td>{$role['id']}</td>";
            echo "<td>{$role['name']}</td>";
            echo "<td>" . ($role['display_name'] ?? 'غير محدد') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>خطأ في فحص الصلاحيات: " . $e->getMessage() . "</p>";
}

echo "<h2>4. اختبار الوصول المباشر</h2>";

echo "<div style='background-color: #fff3cd; padding: 15px; border: 1px solid #ffeaa7; border-radius: 5px;'>";
echo "<h3>اختبار الوصول لصفحة شجرة الحسابات:</h3>";
echo "<p>انقر على الرابط أدناه لاختبار الوصول:</p>";
echo "<p><a href='http://localhost/chart-of-account' target='_blank' style='background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🔗 فتح صفحة شجرة الحسابات</a></p>";

echo "<h3>ما يجب أن تراه:</h3>";
echo "<ul>";
echo "<li><strong>إذا لم تكن مسجل دخول:</strong> ستتم إعادة توجيهك لصفحة تسجيل الدخول</li>";
echo "<li><strong>إذا كنت مسجل دخول بدون صلاحيات:</strong> ستظهر رسالة 'Permission denied'</li>";
echo "<li><strong>إذا كنت مسجل دخول مع الصلاحيات:</strong> ستظهر صفحة شجرة الحسابات (قد تكون فارغة)</li>";
echo "</ul>";
echo "</div>";

echo "<h2>5. الحلول المقترحة</h2>";

echo "<div style='background-color: #d1ecf1; padding: 15px; border: 1px solid #bee5eb; border-radius: 5px;'>";
echo "<h3>إذا كانت المشكلة في تسجيل الدخول:</h3>";
echo "<ol>";
echo "<li><a href='http://localhost/login' target='_blank' style='color: #007bff;'>تسجيل الدخول</a></li>";
echo "<li>استخدم بيانات مستخدم صحيحة</li>";
echo "<li>تأكد من حفظ الجلسة</li>";
echo "</ol>";

echo "<h3>إذا كانت المشكلة في الصلاحيات:</h3>";
echo "<ol>";
echo "<li>تحقق من أن المستخدم له دور مناسب</li>";
echo "<li>تأكد من وجود صلاحية 'manage chart of account'</li>";
echo "<li>قم بإعطاء الصلاحيات المناسبة للمستخدم</li>";
echo "</ol>";

echo "<h3>إذا كانت المشكلة في البيانات:</h3>";
echo "<ol>";
echo "<li><a href='seed_chart_accounts_direct.php' style='color: #007bff;'>إنشاء البيانات الأساسية</a></li>";
echo "<li><a href='fix_created_by_values.php' style='color: #007bff;'>إصلاح قيم created_by</a></li>";
echo "</ol>";
echo "</div>";

echo "<h2>6. أدوات التشخيص الإضافية</h2>";
echo "<p>";
echo "<a href='quick_chart_check.php' style='background-color: #6c757d; color: white; padding: 8px 15px; text-decoration: none; border-radius: 4px; margin-right: 10px;'>فحص سريع</a>";
echo "<a href='comprehensive_chart_diagnosis.php' style='background-color: #fd7e14; color: white; padding: 8px 15px; text-decoration: none; border-radius: 4px; margin-right: 10px;'>فحص شامل</a>";
echo "<a href='debug_chart_simple.php' style='background-color: #20c997; color: white; padding: 8px 15px; text-decoration: none; border-radius: 4px;'>فحص قاعدة البيانات</a>";
echo "</p>";
?>
