# 📁 هيكل الملفات للنقل إلى السيرفر - التحديث الأخير

## 📋 ملخص التعديلات المطبقة

### ✅ إخفاء الشاشات من القائمة الجانبية
### ✅ ترجمة النصوص الإنجليزية إلى العربية  
### ✅ إضافة ترجمات شاملة لصفحات أوامر الاستلام

---

## 📁 الملفات المطلوب نقلها (فقط ملفين)

### **1. ملف القائمة الجانبية (الأساسي)**
```
resources/views/partials/admin/menu.blade.php
```
**التعديلات المطبقة:**
- إخفاء 7 شاشات من قسم نظام نقاط البيع
- إخفاء 5 شاشات من قسم إدارة عمليات الفروع  
- إخفاء 6 شاشات من قسم إدارة العمليات المالية
- تصحيح نصوص أوامر الاستلام للترجمة الصحيحة
- تحديث شروط active class لجميع الأقسام

### **2. ملف الترجمة العربية (الأساسي)**
```
resources/lang/ar.json
```
**التعديلات المطبقة:**
- إصلاح ترجمات معكوسة (Inventory Modification, Branch Cash Management)
- إضافة ترجمات أوامر الاستلام الشاملة
- إضافة ترجمات محتوى الصفحات والأزرار
- إضافة ترجمات الحالات والعمليات

---

## 🎯 الشاشات المخفية (18 شاشة إجمالي)

### **قسم نظام نقاط البيع (7 شاشات):**
- ❌ اقتباس (Quotation)
- ❌ POS V2 - Advanced  
- ❌ شراء (Purchase)
- ❌ مخزون الفرع (Branch Inventory)
- ❌ إعدادات الطباعة (Print Settings)
- ❌ تحويل (Transfer)
- ❌ مستودع (Warehouse)

### **قسم إدارة عمليات الفروع (5 شاشات):**
- ❌ Purchase (المشتريات)
- ❌ Warehouse Transfer (نقل المستودع)
- ❌ Warehouse (المستودع)
- ❌ Product Service (خدمة المنتجات)
- ❌ تاريخ الصلاحية للمنتجات

### **قسم إدارة العمليات المالية (6 شاشات):**
- ❌ معالجة فواتير المستودع
- ❌ التسعير
- ❌ توزيع الحسابات
- ❌ إدارة حدود المخزون
- ❌ عمليات النقد الجاري
- ❌ إدارة المنتجات والخدمات المتقدمة

---

## 🌐 الترجمات المضافة (25+ ترجمة جديدة)

### **ترجمات أوامر الاستلام:**
- Receipt Orders ↔ أوامر الاستلام
- Receipt Orders - Financial Operations Management ↔ أوامر الاستلام - إدارة العمليات المالية
- Receipt Orders - Branch Operations Management ↔ أوامر الاستلام - إدارة عمليات الفروع
- Receipt Orders List ↔ قائمة أوامر الاستلام
- Order Number ↔ رقم الأمر
- Order Type ↔ نوع الأمر
- Vendor/Source ↔ المورد/المصدر
- Warehouse/Branch ↔ المستودع/الفرع
- From Warehouse ↔ من مستودع
- Created By ↔ المستخدم المنشئ
- Total Amount ↔ المبلغ الإجمالي
- Created Date ↔ تاريخ الإنشاء
- Number of Products ↔ عدد المنتجات

### **ترجمات أنواع الأوامر:**
- Goods Receipt ↔ استلام بضاعة
- Inventory Transfer ↔ نقل مخزون

### **ترجمات العمليات والأزرار:**
- Export Excel ↔ تصدير Excel
- Export PDF ↔ تصدير PDF
- Print ↔ طباعة
- View ↔ عرض
- Edit ↔ تحرير
- Delete ↔ حذف
- Actions ↔ الإجراءات

### **ترجمات الحالات:**
- Completed ↔ مكتمل
- In Progress ↔ قيد التنفيذ
- Cancelled ↔ ملغي
- Status ↔ الحالة

### **ترجمات عامة:**
- Home ↔ الرئيسية
- Dashboard ↔ لوحة التحكم
- Financial Operations Management ↔ إدارة العمليات المالية
- Branch Operations Management ↔ إدارة عمليات الفروع

---

## 🚀 خطوات النشر السريع

### **الطريقة المفضلة: رفع عبر File Manager**
```
1. ادخل إلى File Manager في Cloudways
2. انتقل إلى مجلد المشروع
3. ارفع الملفين في المسارات التالية:
   - resources/views/partials/admin/menu.blade.php
   - resources/lang/ar.json
4. تأكد من الكتابة فوق الملفات الموجودة
```

### **أوامر تنظيف Cache (مهمة جداً):**
```bash
php artisan cache:clear
php artisan view:clear  
php artisan config:clear
php artisan route:clear
```

---

## ⚠️ تعليمات مهمة للنشر

### **قبل النشر:**
1. ✅ **عمل نسخة احتياطية** من الملفين الحاليين
2. ✅ تأكد من وجود صلاحيات الكتابة
3. ✅ تأكد من مسار المشروع الصحيح

### **أثناء النشر:**
1. ✅ ارفع `menu.blade.php` أولاً
2. ✅ ارفع `ar.json` ثانياً  
3. ✅ تأكد من استبدال الملفات وليس إنشاء نسخ

### **بعد النشر مباشرة:**
1. ✅ **امسح جميع أنواع Cache** (الأهم)
2. ✅ تحقق من القائمة الجانبية
3. ✅ تحقق من ترجمة أوامر الاستلام
4. ✅ تأكد من إخفاء الشاشات المطلوبة

---

## 📊 النتيجة المتوقعة

### **القائمة الجانبية:**
- ✅ إخفاء 18 شاشة غير مرغوب فيها
- ✅ عرض أوامر الاستلام باللغة العربية الصحيحة
- ✅ ترتيب أنظف وأكثر تنظيماً
- ✅ تحسين تجربة المستخدم

### **الترجمات:**
- ✅ جميع نصوص أوامر الاستلام باللغة العربية
- ✅ ترجمة صحيحة لجميع العناصر
- ✅ إصلاح النصوص المعكوسة
- ✅ تجربة مستخدم محسنة

---

## 🔧 استكشاف الأخطاء

### **إذا لم تظهر التغييرات:**
```bash
# امسح جميع أنواع Cache
php artisan cache:clear
php artisan view:clear
php artisan config:clear
php artisan route:clear

# أعد تشغيل الخادم إذا أمكن
sudo service apache2 restart
# أو
sudo service nginx restart
```

### **إذا ظهرت أخطاء:**
1. تحقق من logs: `storage/logs/laravel.log`
2. تأكد من صحة syntax الملفات
3. استعد النسخة الاحتياطية إذا لزم الأمر

---

## 📞 الدعم والمتابعة

**بعد النشر الناجح:**
- تأكد من عمل جميع الوظائف
- تحقق من عدم وجود أخطاء في logs
- اختبر الترجمات في صفحات مختلفة

**في حالة وجود مشاكل:**
- احتفظ بالنسخة الاحتياطية جاهزة
- راجع خطوات النشر مرة أخرى
- تواصل للحصول على المساعدة الفورية
