<?php
// تشخيص مشكلة شجرة الحسابات
require_once 'vendor/autoload.php';

// تحميل Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Http\Kernel::class);

$response = $kernel->handle(
    $request = Illuminate\Http\Request::capture()
);

use App\Models\ChartOfAccount;
use App\Models\ChartOfAccountType;
use App\Models\ChartOfAccountSubType;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

echo "<h1>تشخيص شجرة الحسابات</h1>";

// 1. فحص الاتصال بقاعدة البيانات
echo "<h2>1. فحص الاتصال بقاعدة البيانات</h2>";
try {
    DB::connection()->getPdo();
    echo "<p style='color: green;'>✓ الاتصال بقاعدة البيانات ناجح</p>";
} catch (\Exception $e) {
    echo "<p style='color: red;'>✗ خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage() . "</p>";
    exit;
}

// 2. فحص وجود الجداول
echo "<h2>2. فحص وجود الجداول</h2>";
$tables = ['chart_of_account_types', 'chart_of_account_sub_types', 'chart_of_accounts'];
foreach ($tables as $table) {
    try {
        $exists = DB::select("SHOW TABLES LIKE '$table'");
        if ($exists) {
            echo "<p style='color: green;'>✓ الجدول $table موجود</p>";
        } else {
            echo "<p style='color: red;'>✗ الجدول $table غير موجود</p>";
        }
    } catch (\Exception $e) {
        echo "<p style='color: red;'>✗ خطأ في فحص الجدول $table: " . $e->getMessage() . "</p>";
    }
}

// 3. فحص بيانات أنواع الحسابات
echo "<h2>3. فحص بيانات أنواع الحسابات</h2>";
try {
    $types = ChartOfAccountType::all();
    echo "<p>عدد أنواع الحسابات: " . $types->count() . "</p>";
    
    if ($types->count() > 0) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>الاسم</th><th>منشئ بواسطة</th></tr>";
        foreach ($types as $type) {
            echo "<tr><td>{$type->id}</td><td>{$type->name}</td><td>{$type->created_by}</td></tr>";
        }
        echo "</table>";
    } else {
        echo "<p style='color: red;'>✗ لا توجد أنواع حسابات</p>";
    }
} catch (\Exception $e) {
    echo "<p style='color: red;'>✗ خطأ في جلب أنواع الحسابات: " . $e->getMessage() . "</p>";
}

// 4. فحص بيانات الأنواع الفرعية
echo "<h2>4. فحص بيانات الأنواع الفرعية</h2>";
try {
    $subTypes = ChartOfAccountSubType::all();
    echo "<p>عدد الأنواع الفرعية: " . $subTypes->count() . "</p>";
    
    if ($subTypes->count() > 0) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>الاسم</th><th>النوع</th><th>منشئ بواسطة</th></tr>";
        foreach ($subTypes as $subType) {
            echo "<tr><td>{$subType->id}</td><td>{$subType->name}</td><td>{$subType->type}</td><td>{$subType->created_by}</td></tr>";
        }
        echo "</table>";
    } else {
        echo "<p style='color: red;'>✗ لا توجد أنواع فرعية</p>";
    }
} catch (\Exception $e) {
    echo "<p style='color: red;'>✗ خطأ في جلب الأنواع الفرعية: " . $e->getMessage() . "</p>";
}

// 5. فحص بيانات الحسابات
echo "<h2>5. فحص بيانات الحسابات</h2>";
try {
    $accounts = ChartOfAccount::all();
    echo "<p>عدد الحسابات: " . $accounts->count() . "</p>";
    
    if ($accounts->count() > 0) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>الكود</th><th>الاسم</th><th>النوع</th><th>النوع الفرعي</th><th>الحساب الأب</th><th>مفعل</th><th>منشئ بواسطة</th></tr>";
        foreach ($accounts as $account) {
            echo "<tr>";
            echo "<td>{$account->id}</td>";
            echo "<td>{$account->code}</td>";
            echo "<td>{$account->name}</td>";
            echo "<td>{$account->type}</td>";
            echo "<td>{$account->sub_type}</td>";
            echo "<td>{$account->parent}</td>";
            echo "<td>" . ($account->is_enabled ? 'نعم' : 'لا') . "</td>";
            echo "<td>{$account->created_by}</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p style='color: red;'>✗ لا توجد حسابات</p>";
    }
} catch (\Exception $e) {
    echo "<p style='color: red;'>✗ خطأ في جلب الحسابات: " . $e->getMessage() . "</p>";
}

// 6. فحص المستخدم الحالي
echo "<h2>6. فحص المستخدم الحالي</h2>";
try {
    // محاولة الحصول على معرف المستخدم من الجلسة
    session_start();
    if (isset($_SESSION['user_id'])) {
        $userId = $_SESSION['user_id'];
        echo "<p>معرف المستخدم من الجلسة: $userId</p>";
    } else {
        echo "<p style='color: orange;'>⚠ لا يوجد مستخدم مسجل دخول في الجلسة</p>";
    }
    
    // فحص المستخدمين في قاعدة البيانات
    $users = DB::table('users')->get();
    echo "<p>عدد المستخدمين في النظام: " . $users->count() . "</p>";
    
    if ($users->count() > 0) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>الاسم</th><th>البريد الإلكتروني</th><th>منشئ بواسطة</th></tr>";
        foreach ($users as $user) {
            echo "<tr>";
            echo "<td>{$user->id}</td>";
            echo "<td>{$user->name}</td>";
            echo "<td>{$user->email}</td>";
            echo "<td>" . ($user->created_by ?? 'غير محدد') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
} catch (\Exception $e) {
    echo "<p style='color: red;'>✗ خطأ في فحص المستخدمين: " . $e->getMessage() . "</p>";
}

// 7. فحص البيانات المفلترة حسب المستخدم
echo "<h2>7. فحص البيانات المفلترة حسب المستخدم</h2>";
try {
    // جرب مع المستخدم الأول
    $firstUser = DB::table('users')->first();
    if ($firstUser) {
        $creatorId = $firstUser->created_by ?? $firstUser->id;
        echo "<p>فحص البيانات للمستخدم: {$firstUser->name} (ID: {$firstUser->id}, Creator ID: $creatorId)</p>";
        
        $filteredTypes = ChartOfAccountType::where('created_by', $creatorId)->get();
        echo "<p>أنواع الحسابات للمستخدم: " . $filteredTypes->count() . "</p>";
        
        $filteredAccounts = ChartOfAccount::where('created_by', $creatorId)->get();
        echo "<p>الحسابات للمستخدم: " . $filteredAccounts->count() . "</p>";
    }
} catch (\Exception $e) {
    echo "<p style='color: red;'>✗ خطأ في فحص البيانات المفلترة: " . $e->getMessage() . "</p>";
}

echo "<h2>8. التوصيات</h2>";
echo "<ul>";
echo "<li>إذا كانت الجداول فارغة، تحتاج إلى تشغيل البذور (seeders)</li>";
echo "<li>إذا كانت البيانات موجودة ولكن لا تظهر، تحقق من صلاحيات المستخدم</li>";
echo "<li>تأكد من أن المستخدم مسجل دخول بشكل صحيح</li>";
echo "<li>تحقق من أن created_by يطابق معرف المستخدم الصحيح</li>";
echo "</ul>";
?>
