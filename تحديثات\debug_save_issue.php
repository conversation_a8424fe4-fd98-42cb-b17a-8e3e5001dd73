<?php
/**
 * تشخيص مشكلة عدم حفظ أمر الاستلام
 * ضع هذا الملف في مجلد public واستدعه من المتصفح
 */

// إعدادات قاعدة البيانات - عدل هذه القيم حسب إعداداتك
$host = 'localhost';
$dbname = 'your_database_name';
$username = 'your_username';
$password = 'your_password';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h1>🔍 تشخيص مشكلة حفظ أمر الاستلام</h1>";
    echo "<style>
        body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        .info { color: blue; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
        table { border-collapse: collapse; width: 100%; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }
        th { background-color: #f2f2f2; }
        .code { background: #f5f5f5; padding: 10px; margin: 10px 0; }
    </style>";

    // 1. فحص الجداول المطلوبة لأوامر الاستلام
    echo "<div class='section'>";
    echo "<h2>1. فحص الجداول المطلوبة</h2>";
    
    $requiredTables = [
        'receipt_orders' => 'جدول أوامر الاستلام الرئيسي',
        'receipt_order_products' => 'جدول منتجات أوامر الاستلام',
        'product_services' => 'جدول المنتجات',
        'warehouses' => 'جدول المستودعات',
        'warehouse_products' => 'جدول مخزون المستودعات',
        'venders' => 'جدول الموردين',
        'users' => 'جدول المستخدمين'
    ];
    
    $missingTables = [];
    foreach ($requiredTables as $table => $description) {
        $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
        if ($stmt->rowCount() > 0) {
            echo "<div class='success'>✅ $table - $description</div>";
        } else {
            echo "<div class='error'>❌ $table - $description (مفقود)</div>";
            $missingTables[] = $table;
        }
    }
    echo "</div>";

    // 2. إنشاء الجداول المفقودة
    if (!empty($missingTables)) {
        echo "<div class='section'>";
        echo "<h2>2. إنشاء الجداول المفقودة</h2>";
        
        if (in_array('receipt_orders', $missingTables)) {
            echo "<div class='warning'>⚠️ إنشاء جدول receipt_orders...</div>";
            $sql = "
            CREATE TABLE `receipt_orders` (
                `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
                `order_number` varchar(255) NOT NULL,
                `order_type` enum('استلام بضاعة','نقل بضاعة','أمر إخراج') NOT NULL,
                `vendor_id` bigint(20) UNSIGNED DEFAULT NULL,
                `warehouse_id` bigint(20) UNSIGNED NOT NULL,
                `from_warehouse_id` bigint(20) UNSIGNED DEFAULT NULL,
                `invoice_number` varchar(255) DEFAULT NULL,
                `invoice_total` decimal(15,2) DEFAULT NULL,
                `invoice_date` date DEFAULT NULL,
                `has_return` tinyint(1) NOT NULL DEFAULT 0,
                `total_products` int(11) NOT NULL DEFAULT 0,
                `total_amount` decimal(15,2) NOT NULL DEFAULT 0.00,
                `notes` text DEFAULT NULL,
                `exit_reason` enum('فقدان','منتهي الصلاحية','تلف/خراب','بيع بالتجزئة') DEFAULT NULL,
                `exit_date` date DEFAULT NULL,
                `responsible_person` varchar(255) DEFAULT NULL,
                `created_by` bigint(20) UNSIGNED NOT NULL,
                `created_at` timestamp NULL DEFAULT NULL,
                `updated_at` timestamp NULL DEFAULT NULL,
                PRIMARY KEY (`id`),
                UNIQUE KEY `receipt_orders_order_number_unique` (`order_number`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
            ";
            
            try {
                $pdo->exec($sql);
                echo "<div class='success'>✅ تم إنشاء جدول receipt_orders</div>";
            } catch (Exception $e) {
                echo "<div class='error'>❌ فشل إنشاء جدول receipt_orders: " . $e->getMessage() . "</div>";
            }
        }
        
        if (in_array('receipt_order_products', $missingTables)) {
            echo "<div class='warning'>⚠️ إنشاء جدول receipt_order_products...</div>";
            $sql = "
            CREATE TABLE `receipt_order_products` (
                `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
                `receipt_order_id` bigint(20) UNSIGNED NOT NULL,
                `product_id` bigint(20) UNSIGNED NOT NULL,
                `quantity` decimal(15,2) NOT NULL,
                `unit_cost` decimal(15,2) NOT NULL DEFAULT 0.00,
                `total_cost` decimal(15,2) NOT NULL DEFAULT 0.00,
                `expiry_date` date DEFAULT NULL,
                `is_return` tinyint(1) NOT NULL DEFAULT 0,
                `notes` text DEFAULT NULL,
                `created_at` timestamp NULL DEFAULT NULL,
                `updated_at` timestamp NULL DEFAULT NULL,
                PRIMARY KEY (`id`),
                KEY `receipt_order_products_receipt_order_id_index` (`receipt_order_id`),
                KEY `receipt_order_products_product_id_index` (`product_id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
            ";
            
            try {
                $pdo->exec($sql);
                echo "<div class='success'>✅ تم إنشاء جدول receipt_order_products</div>";
            } catch (Exception $e) {
                echo "<div class='error'>❌ فشل إنشاء جدول receipt_order_products: " . $e->getMessage() . "</div>";
            }
        }
        echo "</div>";
    }

    // 3. اختبار إدراج بيانات تجريبية
    echo "<div class='section'>";
    echo "<h2>3. اختبار إدراج بيانات تجريبية</h2>";
    
    try {
        // اختبار إدراج أمر استلام
        $stmt = $pdo->prepare("
            INSERT INTO receipt_orders (
                order_number, order_type, warehouse_id, 
                total_products, total_amount, created_by, 
                created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, NOW(), NOW())
        ");
        
        $testOrderNumber = 'TEST-' . date('YmdHis');
        $stmt->execute([$testOrderNumber, 'استلام بضاعة', 1, 0, 0.00, 1]);
        $orderId = $pdo->lastInsertId();
        
        echo "<div class='success'>✅ تم إدراج أمر تجريبي بنجاح - ID: $orderId</div>";
        
        // حذف البيانات التجريبية
        $pdo->exec("DELETE FROM receipt_orders WHERE id = $orderId");
        echo "<div class='info'>🗑️ تم حذف البيانات التجريبية</div>";
        
    } catch (Exception $e) {
        echo "<div class='error'>❌ فشل اختبار الإدراج: " . $e->getMessage() . "</div>";
    }
    echo "</div>";

    // 4. فحص البيانات المطلوبة للاختبار
    echo "<div class='section'>";
    echo "<h2>4. فحص البيانات المطلوبة</h2>";
    
    // فحص المستودعات
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM warehouses");
    $warehouseCount = $stmt->fetch()['count'];
    echo "<div class='info'>📦 عدد المستودعات: $warehouseCount</div>";
    
    if ($warehouseCount == 0) {
        echo "<div class='warning'>⚠️ لا توجد مستودعات - إضافة مستودع تجريبي...</div>";
        try {
            $pdo->exec("INSERT INTO warehouses (name, created_by, created_at, updated_at) VALUES ('المستودع الرئيسي', 1, NOW(), NOW())");
            echo "<div class='success'>✅ تم إضافة مستودع تجريبي</div>";
        } catch (Exception $e) {
            echo "<div class='error'>❌ فشل إضافة مستودع: " . $e->getMessage() . "</div>";
        }
    }
    
    // فحص المنتجات
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM product_services");
    $productCount = $stmt->fetch()['count'];
    echo "<div class='info'>📦 عدد المنتجات: $productCount</div>";
    
    if ($productCount == 0) {
        echo "<div class='warning'>⚠️ لا توجد منتجات - إضافة منتجات تجريبية...</div>";
        try {
            $pdo->exec("
                INSERT INTO product_services (name, sku, sale_price, purchase_price, created_by, created_at, updated_at) 
                VALUES 
                ('منتج تجريبي 1', 'TEST001', 10.00, 8.00, 1, NOW(), NOW()),
                ('منتج تجريبي 2', 'TEST002', 15.00, 12.00, 1, NOW(), NOW())
            ");
            echo "<div class='success'>✅ تم إضافة منتجات تجريبية</div>";
        } catch (Exception $e) {
            echo "<div class='error'>❌ فشل إضافة منتجات: " . $e->getMessage() . "</div>";
        }
    }
    
    // فحص الموردين
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM venders");
    $venderCount = $stmt->fetch()['count'];
    echo "<div class='info'>🏪 عدد الموردين: $venderCount</div>";
    
    if ($venderCount == 0) {
        echo "<div class='warning'>⚠️ لا توجد موردين - إضافة مورد تجريبي...</div>";
        try {
            $pdo->exec("INSERT INTO venders (name, created_by, created_at, updated_at) VALUES ('مورد تجريبي', 1, NOW(), NOW())");
            echo "<div class='success'>✅ تم إضافة مورد تجريبي</div>";
        } catch (Exception $e) {
            echo "<div class='error'>❌ فشل إضافة مورد: " . $e->getMessage() . "</div>";
        }
    }
    echo "</div>";

    // 5. اختبار شامل للحفظ
    echo "<div class='section'>";
    echo "<h2>5. اختبار شامل لعملية الحفظ</h2>";
    
    try {
        // جلب بيانات للاختبار
        $warehouse = $pdo->query("SELECT id FROM warehouses LIMIT 1")->fetch();
        $product = $pdo->query("SELECT id FROM product_services LIMIT 1")->fetch();
        $vendor = $pdo->query("SELECT id FROM venders LIMIT 1")->fetch();
        
        if ($warehouse && $product && $vendor) {
            echo "<div class='info'>🧪 بدء اختبار شامل...</div>";
            
            // إنشاء أمر استلام كامل
            $pdo->beginTransaction();
            
            $stmt = $pdo->prepare("
                INSERT INTO receipt_orders (
                    order_number, order_type, vendor_id, warehouse_id,
                    invoice_number, invoice_date, total_products, total_amount,
                    created_by, created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
            ");
            
            $testOrderNumber = 'FULL-TEST-' . date('YmdHis');
            $stmt->execute([
                $testOrderNumber, 'استلام بضاعة', $vendor['id'], $warehouse['id'],
                'INV-TEST-001', date('Y-m-d'), 1, 10.00, 1
            ]);
            
            $orderId = $pdo->lastInsertId();
            
            // إضافة منتج للأمر
            $stmt = $pdo->prepare("
                INSERT INTO receipt_order_products (
                    receipt_order_id, product_id, quantity, unit_cost, total_cost,
                    created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, NOW(), NOW())
            ");
            
            $stmt->execute([$orderId, $product['id'], 5, 2.00, 10.00]);
            
            $pdo->commit();
            
            echo "<div class='success'>✅ تم إنشاء أمر استلام كامل بنجاح!</div>";
            echo "<div class='info'>📋 رقم الأمر: $testOrderNumber</div>";
            echo "<div class='info'>🆔 معرف الأمر: $orderId</div>";
            
            // عرض البيانات المحفوظة
            $stmt = $pdo->prepare("
                SELECT ro.*, rop.quantity, rop.unit_cost, rop.total_cost
                FROM receipt_orders ro
                LEFT JOIN receipt_order_products rop ON ro.id = rop.receipt_order_id
                WHERE ro.id = ?
            ");
            $stmt->execute([$orderId]);
            $result = $stmt->fetch();
            
            if ($result) {
                echo "<div class='success'>✅ تم استرجاع البيانات بنجاح:</div>";
                echo "<div class='code'>";
                echo "رقم الأمر: {$result['order_number']}<br>";
                echo "نوع الأمر: {$result['order_type']}<br>";
                echo "المستودع: {$result['warehouse_id']}<br>";
                echo "المورد: {$result['vendor_id']}<br>";
                echo "الكمية: {$result['quantity']}<br>";
                echo "تكلفة الوحدة: {$result['unit_cost']}<br>";
                echo "التكلفة الإجمالية: {$result['total_cost']}<br>";
                echo "</div>";
            }
            
            // حذف البيانات التجريبية
            $pdo->exec("DELETE FROM receipt_order_products WHERE receipt_order_id = $orderId");
            $pdo->exec("DELETE FROM receipt_orders WHERE id = $orderId");
            echo "<div class='info'>🗑️ تم حذف البيانات التجريبية</div>";
            
        } else {
            echo "<div class='error'>❌ لا توجد بيانات كافية للاختبار</div>";
        }
        
    } catch (Exception $e) {
        $pdo->rollback();
        echo "<div class='error'>❌ فشل الاختبار الشامل: " . $e->getMessage() . "</div>";
    }
    echo "</div>";

    // 6. تعليمات الإصلاح
    echo "<div class='section'>";
    echo "<h2>6. تعليمات الإصلاح</h2>";
    echo "<div class='info'>💡 إذا نجحت جميع الاختبارات أعلاه، فالمشكلة قد تكون في:</div>";
    echo "<ul>";
    echo "<li><strong>JavaScript:</strong> تحقق من Console في المتصفح</li>";
    echo "<li><strong>Validation:</strong> تحقق من صحة البيانات المرسلة</li>";
    echo "<li><strong>CSRF Token:</strong> تحقق من وجود @csrf في النموذج</li>";
    echo "<li><strong>الصلاحيات:</strong> تحقق من صلاحيات المستخدم</li>";
    echo "<li><strong>Laravel Cache:</strong> امسح الكاش</li>";
    echo "</ul>";
    
    echo "<div class='code'>";
    echo "# أوامر مسح الكاش:<br>";
    echo "php artisan cache:clear<br>";
    echo "php artisan config:clear<br>";
    echo "php artisan route:clear<br>";
    echo "php artisan view:clear<br>";
    echo "</div>";
    echo "</div>";

    echo "<div class='section'>";
    echo "<h2>✅ انتهاء التشخيص</h2>";
    echo "<div class='success'>تم إجراء جميع اختبارات التشخيص!</div>";
    echo "</div>";

} catch (PDOException $e) {
    echo "<div class='error'>❌ خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage() . "</div>";
    echo "<div class='info'>💡 تأكد من إعدادات قاعدة البيانات في بداية هذا الملف</div>";
}
?>
