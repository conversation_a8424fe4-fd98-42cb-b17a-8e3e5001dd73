# دليل النشر السريع - أوامر الاستلام لدور Cashier

## 🚀 النشر السريع

### **الطريقة الأولى: استخدام السكريبت التلقائي**

1. **تعديل إعدادات الخادم في السكريبت:**
```bash
# تعديل الملف deploy_cashier_receipt_orders.sh
SERVER_USER="your_username"
SERVER_HOST="your_server_ip"
PROJECT_PATH="/path/to/your/project"
```

2. **تشغيل السكريبت:**
```bash
chmod +x deploy_cashier_receipt_orders.sh
./deploy_cashier_receipt_orders.sh
```

### **الطريقة الثانية: النشر اليدوي**

```bash
# 1. رفع الملفات
scp resources/views/partials/admin/menu.blade.php user@server:/path/to/project/resources/views/partials/admin/
scp app/Http/Controllers/ReceiptOrderController.php user@server:/path/to/project/app/Http/Controllers/
scp resources/views/receipt_order/index.blade.php user@server:/path/to/project/resources/views/receipt_order/

# 2. ضبط الصلاحيات
ssh user@server "chmod 644 /path/to/project/resources/views/partials/admin/menu.blade.php"
ssh user@server "chmod 644 /path/to/project/app/Http/Controllers/ReceiptOrderController.php"
ssh user@server "chmod 644 /path/to/project/resources/views/receipt_order/index.blade.php"

# 3. مسح الكاش
ssh user@server "cd /path/to/project && php artisan cache:clear && php artisan view:clear && php artisan route:clear"
```

## ✅ اختبار سريع

1. **تسجيل الدخول** بحساب لديه دور Cashier
2. **التحقق من القائمة:** يجب أن ترى "أوامر الاستلام" في قسم POS
3. **اختبار الوصول:** النقر على الرابط يجب أن يعرض قائمة أوامر الاستلام
4. **اختبار الإنشاء:** يجب أن ترى زر "إنشاء أمر استلام"
5. **اختبار الوظائف:** إنشاء أمر جديد، عرض التفاصيل، الطباعة

## 🔧 التغييرات المطبقة

### **الملفات المحدثة:**
- ✅ `resources/views/partials/admin/menu.blade.php` - إضافة عنصر القائمة
- ✅ `app/Http/Controllers/ReceiptOrderController.php` - إضافة صلاحيات Cashier
- ✅ `resources/views/receipt_order/index.blade.php` - إظهار زر الإنشاء

### **الصلاحيات الجديدة لدور Cashier:**
- ✅ عرض أوامر الاستلام
- ✅ إنشاء أوامر استلام جديدة
- ✅ عرض تفاصيل أوامر الاستلام
- ✅ طباعة أوامر الاستلام
- ✅ تحميل PDF لأوامر الاستلام

## 🚨 ملاحظات مهمة

1. **تأكد من وجود دور Cashier** في النظام قبل النشر
2. **اختبر على بيئة التطوير** قبل النشر على الإنتاج
3. **احتفظ بنسخة احتياطية** من الملفات الأصلية
4. **تحقق من عمل جميع الوظائف** بعد النشر

## 🔄 التراجع السريع

إذا واجهت مشاكل، يمكنك التراجع بسرعة:

```bash
# إزالة السطر المضاف من القائمة الجانبية (الأسطر 1410-1416)
# إزالة "|| Auth::user()->hasRole('Cashier')" من الكونترولر
# إرجاع شرط زر الإنشاء إلى @can('manage warehouse') فقط
```

## 📞 الدعم

إذا واجهت أي مشاكل:
1. تحقق من ملف `CASHIER_RECEIPT_ORDERS_DEPLOYMENT.md` للتفاصيل الكاملة
2. تحقق من سجلات الأخطاء في Laravel
3. تأكد من صحة صلاحيات الملفات
4. تأكد من مسح الكاش بشكل صحيح
