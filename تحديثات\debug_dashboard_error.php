<?php
/**
 * ملف تشخيص خطأ 500 في الداشبورد
 * ضع هذا الملف في المجلد الجذر للمشروع وشغله من المتصفح
 */

// تفعيل عرض الأخطاء
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🔍 تشخيص خطأ 500 في الداشبورد</h1>";
echo "<style>
body { font-family: Arial, sans-serif; margin: 20px; }
.success { color: green; }
.error { color: red; }
.warning { color: orange; }
.info { color: blue; }
.section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
</style>";

// 1. فحص ملفات Laravel الأساسية
echo "<div class='section'>";
echo "<h2>1. فحص ملفات Laravel الأساسية</h2>";

$required_files = [
    'vendor/autoload.php',
    'bootstrap/app.php',
    '.env',
    'app/Http/Controllers/DashboardController.php',
    'app/Models/User.php',
    'routes/web.php'
];

foreach ($required_files as $file) {
    if (file_exists($file)) {
        echo "<span class='success'>✅ {$file} موجود</span><br>";
    } else {
        echo "<span class='error'>❌ {$file} مفقود</span><br>";
    }
}
echo "</div>";

// 2. فحص الكونترولرات الجديدة
echo "<div class='section'>";
echo "<h2>2. فحص الكونترولرات الجديدة</h2>";

$new_controllers = [
    'app/Http/Controllers/FinancialReceiptOrderController.php',
    'app/Http/Controllers/BranchReceiptOrderController.php',
    'app/Http/Controllers/ReceiptOrderPdfSimpleController.php'
];

foreach ($new_controllers as $controller) {
    if (file_exists($controller)) {
        echo "<span class='success'>✅ {$controller} موجود</span><br>";
        
        // فحص صحة الكود
        $content = file_get_contents($controller);
        if (strpos($content, '<?php') === 0) {
            echo "<span class='info'>   📝 الملف يبدأ بـ PHP tag صحيح</span><br>";
        } else {
            echo "<span class='error'>   ❌ الملف لا يبدأ بـ PHP tag صحيح</span><br>";
        }
        
        if (strpos($content, 'class') !== false) {
            echo "<span class='info'>   📝 الملف يحتوي على class</span><br>";
        } else {
            echo "<span class='error'>   ❌ الملف لا يحتوي على class</span><br>";
        }
    } else {
        echo "<span class='error'>❌ {$controller} مفقود</span><br>";
    }
}
echo "</div>";

// 3. فحص النماذج
echo "<div class='section'>";
echo "<h2>3. فحص النماذج</h2>";

$models = [
    'app/Models/ReceiptOrder.php',
    'app/Models/ReceiptOrderProduct.php'
];

foreach ($models as $model) {
    if (file_exists($model)) {
        echo "<span class='success'>✅ {$model} موجود</span><br>";
    } else {
        echo "<span class='warning'>⚠️ {$model} مفقود (قد يكون اختياري)</span><br>";
    }
}
echo "</div>";

// 4. فحص قاعدة البيانات
echo "<div class='section'>";
echo "<h2>4. فحص قاعدة البيانات</h2>";

try {
    // محاولة تحميل Laravel
    require_once 'vendor/autoload.php';
    
    // فحص ملف .env
    if (file_exists('.env')) {
        $env_content = file_get_contents('.env');
        
        if (strpos($env_content, 'DB_DATABASE=') !== false) {
            echo "<span class='success'>✅ إعدادات قاعدة البيانات موجودة في .env</span><br>";
        } else {
            echo "<span class='error'>❌ إعدادات قاعدة البيانات مفقودة في .env</span><br>";
        }
        
        // استخراج إعدادات قاعدة البيانات
        preg_match('/DB_HOST=(.*)/', $env_content, $host_match);
        preg_match('/DB_DATABASE=(.*)/', $env_content, $db_match);
        preg_match('/DB_USERNAME=(.*)/', $env_content, $user_match);
        
        if (!empty($host_match[1])) {
            echo "<span class='info'>   📝 DB_HOST: " . trim($host_match[1]) . "</span><br>";
        }
        if (!empty($db_match[1])) {
            echo "<span class='info'>   📝 DB_DATABASE: " . trim($db_match[1]) . "</span><br>";
        }
        if (!empty($user_match[1])) {
            echo "<span class='info'>   📝 DB_USERNAME: " . trim($user_match[1]) . "</span><br>";
        }
    }
    
} catch (Exception $e) {
    echo "<span class='error'>❌ خطأ في تحميل Laravel: " . $e->getMessage() . "</span><br>";
}
echo "</div>";

// 5. فحص الصلاحيات
echo "<div class='section'>";
echo "<h2>5. فحص صلاحيات الملفات</h2>";

$directories = [
    'storage',
    'bootstrap/cache',
    'app',
    'resources'
];

foreach ($directories as $dir) {
    if (is_dir($dir)) {
        $perms = substr(sprintf('%o', fileperms($dir)), -4);
        if ($perms >= '0755') {
            echo "<span class='success'>✅ {$dir} - صلاحيات: {$perms}</span><br>";
        } else {
            echo "<span class='warning'>⚠️ {$dir} - صلاحيات: {$perms} (قد تحتاج 755)</span><br>";
        }
    } else {
        echo "<span class='error'>❌ {$dir} غير موجود</span><br>";
    }
}
echo "</div>";

// 6. فحص سجل الأخطاء
echo "<div class='section'>";
echo "<h2>6. آخر أخطاء Laravel</h2>";

$log_file = 'storage/logs/laravel.log';
if (file_exists($log_file)) {
    $log_content = file_get_contents($log_file);
    $lines = explode("\n", $log_content);
    $recent_lines = array_slice($lines, -20); // آخر 20 سطر
    
    echo "<pre style='background: #f5f5f5; padding: 10px; border-radius: 5px; max-height: 300px; overflow-y: scroll;'>";
    foreach ($recent_lines as $line) {
        if (!empty(trim($line))) {
            if (strpos($line, 'ERROR') !== false) {
                echo "<span class='error'>{$line}</span>\n";
            } elseif (strpos($line, 'WARNING') !== false) {
                echo "<span class='warning'>{$line}</span>\n";
            } else {
                echo "{$line}\n";
            }
        }
    }
    echo "</pre>";
} else {
    echo "<span class='warning'>⚠️ ملف سجل الأخطاء غير موجود</span><br>";
}
echo "</div>";

// 7. اختبار بسيط للداشبورد
echo "<div class='section'>";
echo "<h2>7. اختبار الوصول للداشبورد</h2>";

try {
    $dashboard_url = (isset($_SERVER['HTTPS']) ? 'https' : 'http') . '://' . $_SERVER['HTTP_HOST'] . '/dashboard';
    echo "<span class='info'>📝 رابط الداشبورد: <a href='{$dashboard_url}' target='_blank'>{$dashboard_url}</a></span><br>";
    echo "<span class='info'>📝 اضغط على الرابط لاختبار الوصول</span><br>";
} catch (Exception $e) {
    echo "<span class='error'>❌ خطأ في تحديد رابط الداشبورد</span><br>";
}
echo "</div>";

// 8. نصائح الإصلاح
echo "<div class='section'>";
echo "<h2>8. نصائح الإصلاح</h2>";
echo "<ul>";
echo "<li><strong>إذا كان هناك خطأ في الكونترولرات:</strong> تأكد من صحة الكود وعدم وجود أخطاء نحوية</li>";
echo "<li><strong>إذا كان هناك خطأ في قاعدة البيانات:</strong> تأكد من صحة إعدادات .env وتشغيل الهجرة</li>";
echo "<li><strong>إذا كان هناك خطأ في الصلاحيات:</strong> شغل: chmod -R 755 storage bootstrap/cache</li>";
echo "<li><strong>إذا كان هناك خطأ في الكاش:</strong> شغل: php artisan cache:clear && php artisan config:clear</li>";
echo "<li><strong>إذا استمر الخطأ:</strong> فعل وضع التطوير في .env: APP_DEBUG=true</li>";
echo "</ul>";
echo "</div>";

echo "<div class='section'>";
echo "<h2>✅ انتهى التشخيص</h2>";
echo "<p>إذا وجدت أخطاء، اتبع النصائح أعلاه أو راسلني بتفاصيل الأخطاء المعروضة.</p>";
echo "</div>";
?>
