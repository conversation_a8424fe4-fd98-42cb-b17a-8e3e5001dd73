# 🔧 إصلاح العلاقات وعرض بيانات العملاء

## 🔍 **المشكلة المكتشفة:**

### **❌ العلاقات المفقودة:**
- نموذج `Customer` لم يحتوي على علاقات مع جداول `pos` و `pos_v2`
- الاستعلامات كانت تفشل لعدم وجود العلاقات المطلوبة
- النظام لم يكن يجد بيانات العملاء مع مبيعاتهم

### **❌ مشاكل في الاستعلامات:**
- لم تكن تدعم كلا النظامين (POS Classic + POS V2)
- لم تجمع البيانات من النظامين معاً
- لم تحسب الإحصائيات بشكل صحيح

---

## 🔧 **الحلول المطبقة:**

### **1. إضافة العلاقات في نموذج Customer:**

```php
// في app/Models/Customer.php

/**
 * علاقة العميل مع فواتير POS Classic
 */
public function pos()
{
    return $this->hasMany('App\Models\Pos', 'customer_id', 'id');
}

/**
 * علاقة العميل مع فواتير POS V2
 */
public function posV2()
{
    return $this->hasMany('App\Models\PosV2', 'customer_id', 'id');
}

/**
 * جلب جميع فواتير العميل من كلا النظامين
 */
public function getAllPosInvoices()
{
    $posClassic = $this->pos()->get();
    $posV2 = $this->posV2()->get();
    
    return $posClassic->merge($posV2);
}
```

### **2. إصلاح دالة تحليل العملاء:**

#### **أ. العملاء النشطون من كلا النظامين:**
```php
// البحث في POS Classic
$activeCustomersFromPos = Customer::where('created_by', $creatorId)
    ->whereHas('pos', function($q) use ($dateFrom, $dateTo, $warehouseId) {
        $q->whereBetween('pos_date', [$dateFrom, $dateTo]);
        if ($warehouseId) {
            $q->where('warehouse_id', $warehouseId);
        }
    })
    ->pluck('id');

// البحث في POS V2
$activeCustomersFromPosV2 = Customer::where('created_by', $creatorId)
    ->whereHas('posV2', function($q) use ($dateFrom, $dateTo, $warehouseId) {
        $q->whereBetween('pos_date', [$dateFrom, $dateTo]);
        if ($warehouseId) {
            $q->where('warehouse_id', $warehouseId);
        }
    })
    ->pluck('id');

// دمج النتائج
$activeCustomerIds = $activeCustomersFromPos->merge($activeCustomersFromPosV2)->unique();
$activeCustomers = $activeCustomerIds->count();
```

#### **ب. العملاء الجدد من كلا النظامين:**
```php
// العملاء الجدد من POS Classic
$newCustomersFromPos = Customer::where('created_by', $creatorId)
    ->whereHas('pos', function($q) use ($dateFrom, $dateTo, $warehouseId) {
        $q->whereBetween('pos_date', [$dateFrom, $dateTo]);
        if ($warehouseId) {
            $q->where('warehouse_id', $warehouseId);
        }
    })
    ->whereDoesntHave('pos', function($q) use ($dateFrom, $warehouseId) {
        $q->where('pos_date', '<', $dateFrom);
        if ($warehouseId) {
            $q->where('warehouse_id', $warehouseId);
        }
    })
    ->pluck('id');

// العملاء الجدد من POS V2
$newCustomersFromPosV2 = Customer::where('created_by', $creatorId)
    ->whereHas('posV2', function($q) use ($dateFrom, $dateTo, $warehouseId) {
        $q->whereBetween('pos_date', [$dateFrom, $dateTo]);
        if ($warehouseId) {
            $q->where('warehouse_id', $warehouseId);
        }
    })
    ->whereDoesntHave('posV2', function($q) use ($dateFrom, $warehouseId) {
        $q->where('pos_date', '<', $dateFrom);
        if ($warehouseId) {
            $q->where('warehouse_id', $warehouseId);
        }
    })
    ->pluck('id');

// دمج النتائج
$newCustomerIds = $newCustomersFromPos->merge($newCustomersFromPosV2)->unique();
$newCustomers = $newCustomerIds->count();
```

### **3. دوال مساعدة لجلب أفضل العملاء:**

#### **أ. من POS Classic:**
```php
private function getTopCustomersFromPos($creatorId, $dateFrom, $dateTo, $warehouseId = null)
{
    $query = DB::table('pos')
        ->join('customers', 'pos.customer_id', '=', 'customers.id')
        ->leftJoin('pos_payments', 'pos.id', '=', 'pos_payments.pos_id')
        ->where('pos.created_by', $creatorId)
        ->whereBetween('pos.pos_date', [$dateFrom, $dateTo]);

    if ($warehouseId) {
        $query->where('pos.warehouse_id', $warehouseId);
    }

    return $query->select(
            'customers.id',
            'customers.name',
            'customers.contact',
            'customers.email',
            DB::raw('COUNT(DISTINCT pos.id) as total_orders'),
            DB::raw('COALESCE(SUM(pos_payments.amount), 0) as total_spent'),
            DB::raw('COALESCE(AVG(pos_payments.amount), 0) as avg_order_value'),
            DB::raw('MAX(pos.pos_date) as last_purchase_date'),
            DB::raw('MIN(pos.pos_date) as first_purchase_date')
        )
        ->groupBy('customers.id', 'customers.name', 'customers.contact', 'customers.email')
        ->get()
        ->toArray();
}
```

#### **ب. من POS V2:**
```php
private function getTopCustomersFromPosV2($creatorId, $dateFrom, $dateTo, $warehouseId = null)
{
    // التحقق من وجود جدول pos_v2
    if (!Schema::hasTable('pos_v2') || !Schema::hasTable('pos_v2_payments')) {
        return [];
    }

    $query = DB::table('pos_v2')
        ->join('customers', 'pos_v2.customer_id', '=', 'customers.id')
        ->leftJoin('pos_v2_payments', 'pos_v2.id', '=', 'pos_v2_payments.pos_id')
        ->where('pos_v2.created_by', $creatorId)
        ->whereBetween('pos_v2.pos_date', [$dateFrom, $dateTo]);

    if ($warehouseId) {
        $query->where('pos_v2.warehouse_id', $warehouseId);
    }

    return $query->select(
            'customers.id',
            'customers.name',
            'customers.contact',
            'customers.email',
            DB::raw('COUNT(DISTINCT pos_v2.id) as total_orders'),
            DB::raw('COALESCE(SUM(pos_v2_payments.amount), 0) as total_spent'),
            DB::raw('COALESCE(AVG(pos_v2_payments.amount), 0) as avg_order_value'),
            DB::raw('MAX(pos_v2.pos_date) as last_purchase_date'),
            DB::raw('MIN(pos_v2.pos_date) as first_purchase_date')
        )
        ->groupBy('customers.id', 'customers.name', 'customers.contact', 'customers.email')
        ->get()
        ->toArray();
}
```

### **4. دمج البيانات من النظامين:**

```php
// جلب أفضل العملاء من كلا النظامين وجمعهم
$topCustomersFromPos = $this->getTopCustomersFromPos($creatorId, $dateFrom, $dateTo, $warehouseId);
$topCustomersFromPosV2 = $this->getTopCustomersFromPosV2($creatorId, $dateFrom, $dateTo, $warehouseId);

// دمج وترتيب العملاء
$allTopCustomers = collect($topCustomersFromPos)->merge($topCustomersFromPosV2);

// تجميع البيانات حسب العميل
$groupedCustomers = $allTopCustomers->groupBy('id')->map(function($customerGroup) {
    $first = $customerGroup->first();
    return [
        'id' => $first->id,
        'name' => $first->name,
        'contact' => $first->contact,
        'email' => $first->email,
        'total_orders' => $customerGroup->sum('total_orders'),
        'total_spent' => $customerGroup->sum('total_spent'),
        'avg_order_value' => $customerGroup->avg('avg_order_value'),
        'first_purchase_date' => $customerGroup->min('first_purchase_date'),
        'last_purchase_date' => $customerGroup->max('last_purchase_date')
    ];
});

$topCustomers = $groupedCustomers->sortByDesc('total_spent')->take(15)->values();
```

---

## 📊 **العلاقات المصححة:**

### **🔗 جدول customers:**
- **id** (Primary Key)
- **customer_id** (معرف العميل الفريد)
- **name, contact, email** (بيانات العميل)
- **created_by** (معرف المنشئ)

### **🔗 جدول pos (POS Classic):**
- **id** (Primary Key)
- **customer_id** → **customers.id** (Foreign Key)
- **warehouse_id** → **warehouses.id**
- **pos_date** (تاريخ الفاتورة)
- **created_by** (معرف المنشئ)

### **🔗 جدول pos_v2 (POS V2):**
- **id** (Primary Key)
- **customer_id** → **customers.id** (Foreign Key)
- **warehouse_id** → **warehouses.id**
- **pos_date** (تاريخ الفاتورة)
- **created_by** (معرف المنشئ)

### **🔗 جدول pos_payments:**
- **id** (Primary Key)
- **pos_id** → **pos.id** (Foreign Key)
- **amount** (مبلغ الدفع)

### **🔗 جدول pos_v2_payments:**
- **id** (Primary Key)
- **pos_id** → **pos_v2.id** (Foreign Key)
- **amount** (مبلغ الدفع)

---

## 🎯 **النتائج المتوقعة:**

### **✅ الآن يعمل:**
1. **عرض بيانات العملاء الفعلية** من قاعدة البيانات
2. **إحصائيات دقيقة** للعملاء النشطين والجدد
3. **جدول أفضل العملاء** مع بياناتهم الحقيقية
4. **دمج البيانات** من POS Classic و POS V2
5. **فلاتر المستودع والتاريخ** تعمل بشكل صحيح

### **📊 البيانات المعروضة:**
- **أسماء العملاء الحقيقية**
- **أرقام هواتفهم وإيميلاتهم**
- **عدد طلباتهم الفعلي**
- **إجمالي إنفاقهم الحقيقي**
- **تواريخ أول وآخر شراء**
- **تصنيف العميل (جديد/متكرر)**

---

## 🧪 **اختبار النظام:**

### **1. اذهب لتبويب "تحليل العملاء":**
```
1. اضغط على تبويب "تحليل العملاء"
2. يجب أن تظهر الإحصائيات الحقيقية
3. يجب أن يظهر جدول العملاء مع بياناتهم الفعلية
```

### **2. اختبر الفلاتر:**
```
1. اختر مستودع محدد
2. غير التاريخ من وإلى
3. اضغط "تحديث البيانات"
4. يجب أن تتحدث البيانات حسب الفلاتر
```

### **3. تحقق من البيانات:**
```
1. أسماء العملاء يجب أن تكون حقيقية
2. أرقام الهواتف والإيميلات صحيحة
3. مبالغ الإنفاق حقيقية من المبيعات
4. التواريخ صحيحة ومنطقية
```

---

## 🎉 **النتيجة:**

**تم إصلاح جميع العلاقات وتحليل العملاء يعرض الآن البيانات الفعلية! 🚀**

### **✅ يعمل الآن:**
- 👥 **بيانات العملاء الحقيقية**
- 💰 **مبالغ الإنفاق الفعلية**
- 📊 **إحصائيات دقيقة**
- 🔍 **فلاتر متقدمة**
- 📅 **تحليل زمني صحيح**

**النظام جاهز لعرض تحليل شامل ودقيق للعملاء! 🎯**
