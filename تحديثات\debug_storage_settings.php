<?php
require_once 'vendor/autoload.php';

// Load Laravel app
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== Storage Settings Debug ===\n\n";

// Get storage settings
$settings = \App\Models\Utility::getStorageSetting();
echo "Current Storage Settings:\n";
print_r($settings);

echo "\n=== File System Configuration ===\n";
echo "Storage Path: " . storage_path() . "\n";
echo "Public Path: " . public_path() . "\n";
echo "Storage URL: " . config('filesystems.disks.local.url') . "\n";

echo "\n=== Directory Structure ===\n";
echo "Storage/uploads exists: " . (is_dir(storage_path('uploads')) ? 'YES' : 'NO') . "\n";
echo "Storage/uploads/avatar exists: " . (is_dir(storage_path('uploads/avatar')) ? 'YES' : 'NO') . "\n";
echo "Storage/uploads/logo exists: " . (is_dir(storage_path('uploads/logo')) ? 'YES' : 'NO') . "\n";

echo "Public/storage exists: " . (is_dir(public_path('storage')) ? 'YES' : 'NO') . "\n";
echo "Public/storage/avatar exists: " . (is_dir(public_path('storage/avatar')) ? 'YES' : 'NO') . "\n";
echo "Public/storage/logo exists: " . (is_dir(public_path('storage/logo')) ? 'YES' : 'NO') . "\n";

echo "\n=== Sample File Paths ===\n";
$avatarPath = \App\Models\Utility::get_file('uploads/avatar');
echo "Avatar path from get_file(): " . $avatarPath . "\n";

$logoPath = \App\Models\Utility::get_file('uploads/logo');
echo "Logo path from get_file(): " . $logoPath . "\n";

echo "\n=== Sample Files ===\n";
if (is_dir(storage_path('uploads/avatar'))) {
    $avatarFiles = scandir(storage_path('uploads/avatar'));
    echo "Avatar files in storage: " . implode(', ', array_slice($avatarFiles, 2, 5)) . "\n";
}

if (is_dir(public_path('storage/avatar'))) {
    $publicAvatarFiles = scandir(public_path('storage/avatar'));
    echo "Avatar files in public: " . implode(', ', array_slice($publicAvatarFiles, 2, 5)) . "\n";
}

echo "\n=== URL Generation Test ===\n";
echo "URL for avatar.png: " . \App\Models\Utility::get_file('uploads/avatar') . '/avatar.png' . "\n";
echo "Asset URL for avatar.png: " . asset('storage/avatar/avatar.png') . "\n";

echo "\nDone.\n";
