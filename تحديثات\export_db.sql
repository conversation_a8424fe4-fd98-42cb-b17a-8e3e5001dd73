-- Export database
SELECT * INTO OUTFILE 'C:/laragon/www/kk/b/COPYSQL/ee_database_backup.sql'
FROM (
    SELECT CONCAT(
        'CREATE DATABASE IF NOT EXISTS `ee`;\n',
        'USE `ee`;\n\n'
    )
) AS header;

-- Export table structure and data for each table
SELECT GROUP_CONCAT(
    CONCAT(
        '-- Table structure for table `', table_name, '`\n',
        'DROP TABLE IF EXISTS `', table_name, '`;\n',
        'CREATE TABLE `', table_name, '` (\n',
        -- Get table structure
        (SELECT GROUP_CONCAT(
            CONCAT('  `', column_name, '` ', column_type, 
                IF(is_nullable = 'NO', ' NOT NULL', ''),
                IF(column_default IS NOT NULL, CONCAT(' DEFAULT ', column_default), ''),
                IF(extra != '', CONCAT(' ', extra), ''),
                ',\n'
            )
            ORDER BY ordinal_position
            SEPARATOR ''
        ) FROM information_schema.columns WHERE table_schema = 'ee' AND table_name = table_name),
        -- Get primary key
        (SELECT IF(COUNT(*) > 0, 
            CONCAT('  PRIMARY KEY (', GROUP_CONCAT('`', column_name, '`' SEPARATOR ', '), ')\n'),
            '')
        FROM information_schema.key_column_usage
        WHERE table_schema = 'ee' AND table_name = table_name AND constraint_name = 'PRIMARY'),
        ');\n\n',
        -- Get table data
        'INSERT INTO `', table_name, '` VALUES\n',
        -- This would need to be replaced with actual data export
        '-- Data export would go here\n\n'
    )
    SEPARATOR '\n'
) FROM information_schema.tables WHERE table_schema = 'ee' AND table_type = 'BASE TABLE';
