# دليل نشر ملفات الهجرة - أوا<PERSON>ر الاستلام

## 📁 **ملفات الهجرة المطلوبة**

### **الملفات الجاهزة للنقل:**
```
database/migrations/
├── 2024_01_15_000001_create_receipt_orders_table.php        ✅ جدول أوامر الاستلام
├── 2024_01_15_000002_create_receipt_order_products_table.php ✅ جدول منتجات الأوامر
└── run_migrations.sql                                        ✅ ملف SQL مباشر
```

## 🚀 **خطوات النشر**

### **المرحلة 1: نقل ملفات الهجرة**
```bash
# نقل ملفات Laravel Migration
scp database/migrations/2024_01_15_000001_create_receipt_orders_table.php user@server:/path/to/project/database/migrations/
scp database/migrations/2024_01_15_000002_create_receipt_order_products_table.php user@server:/path/to/project/database/migrations/

# نقل ملف SQL المباشر (احتياطي)
scp run_migrations.sql user@server:/path/to/project/

# التحقق من النقل
ssh user@server "ls -la /path/to/project/database/migrations/2024_01_15_*"
```

### **المرحلة 2: تشغيل الهجرة**

#### **الطريقة الأولى: Laravel Artisan (الأفضل)**
```bash
# الاتصال بالخادم
ssh user@server

# الانتقال لمجلد المشروع
cd /path/to/project

# التحقق من حالة قاعدة البيانات
php artisan migrate:status

# تشغيل الهجرة الجديدة
php artisan migrate

# التحقق من نجاح الهجرة
php artisan migrate:status | grep receipt
```

#### **الطريقة الثانية: SQL مباشرة (احتياطي)**
```bash
# في حالة فشل Laravel Artisan
mysql -u username -p database_name < run_migrations.sql

# أو باستخدام phpMyAdmin
# 1. اذهب لـ phpMyAdmin
# 2. اختر قاعدة البيانات
# 3. اذهب لتبويب "Import"
# 4. ارفع ملف run_migrations.sql
# 5. اضغط "Go"
```

## 🔍 **التحقق من نجاح الهجرة**

### **1. التحقق من وجود الجداول:**
```sql
-- عرض جميع الجداول المتعلقة بأوامر الاستلام
SHOW TABLES LIKE 'receipt_%';

-- النتيجة المتوقعة:
-- receipt_orders
-- receipt_order_products
```

### **2. التحقق من هيكل الجداول:**
```sql
-- فحص جدول أوامر الاستلام
DESCRIBE receipt_orders;

-- فحص جدول منتجات الأوامر
DESCRIBE receipt_order_products;
```

### **3. التحقق من المفاتيح الخارجية:**
```sql
-- عرض المفاتيح الخارجية لجدول أوامر الاستلام
SELECT 
    CONSTRAINT_NAME,
    COLUMN_NAME,
    REFERENCED_TABLE_NAME,
    REFERENCED_COLUMN_NAME
FROM information_schema.KEY_COLUMN_USAGE 
WHERE TABLE_NAME = 'receipt_orders' 
AND REFERENCED_TABLE_NAME IS NOT NULL;
```

## 🧪 **اختبار الجداول**

### **1. اختبار إدراج بيانات تجريبية:**
```sql
-- اختبار جدول receipt_orders
INSERT INTO receipt_orders (
    order_number, order_type, warehouse_id, 
    total_products, total_amount, created_by, 
    created_at, updated_at
) VALUES (
    'RO-2024-TEST-001', 'استلام بضاعة', 1, 
    0, 0.00, 1, 
    NOW(), NOW()
);

-- التحقق من الإدراج
SELECT * FROM receipt_orders WHERE order_number = 'RO-2024-TEST-001';

-- حذف البيانات التجريبية
DELETE FROM receipt_orders WHERE order_number = 'RO-2024-TEST-001';
```

### **2. اختبار العلاقات:**
```sql
-- اختبار العلاقة مع جدول المستودعات
SELECT 
    ro.order_number,
    w.name as warehouse_name
FROM receipt_orders ro
LEFT JOIN warehouses w ON ro.warehouse_id = w.id
LIMIT 5;

-- اختبار العلاقة مع جدول المستخدمين
SELECT 
    ro.order_number,
    u.name as creator_name
FROM receipt_orders ro
LEFT JOIN users u ON ro.created_by = u.id
LIMIT 5;
```

## 🔧 **استكشاف الأخطاء**

### **خطأ: "Table already exists"**
```bash
# إذا كان الجدول موجود بالفعل
php artisan migrate:rollback --step=2
php artisan migrate
```

### **خطأ: "Foreign key constraint fails"**
```sql
-- التحقق من وجود الجداول المرجعية
SHOW TABLES LIKE 'warehouses';
SHOW TABLES LIKE 'users';
SHOW TABLES LIKE 'venders';
SHOW TABLES LIKE 'product_services';
```

### **خطأ: "Access denied"**
```bash
# التحقق من صلاحيات قاعدة البيانات
mysql -u username -p -e "SHOW GRANTS;"
```

### **خطأ: "Migration file not found"**
```bash
# التحقق من وجود ملفات الهجرة
ls -la database/migrations/2024_01_15_*

# إعادة تحميل autoload
composer dump-autoload
```

## 📋 **قائمة التحقق النهائية**

### **قبل الهجرة:**
- [ ] **نسخ احتياطي** من قاعدة البيانات
- [ ] **التحقق من صلاحيات** قاعدة البيانات
- [ ] **التأكد من وجود** الجداول المرجعية

### **أثناء الهجرة:**
- [ ] **نقل ملفات الهجرة** بنجاح
- [ ] **تشغيل الهجرة** بدون أخطاء
- [ ] **التحقق من إنشاء الجداول**

### **بعد الهجرة:**
- [ ] **اختبار هيكل الجداول**
- [ ] **اختبار المفاتيح الخارجية**
- [ ] **اختبار إدراج البيانات**
- [ ] **اختبار العلاقات**
- [ ] **اختبار الصفحات الجديدة**

## 🎯 **الأمر الشامل للهجرة**

```bash
#!/bin/bash

# متغيرات
SERVER_USER="your_username"
SERVER_HOST="your_server_ip"
PROJECT_PATH="/path/to/your/project"
DB_USER="database_username"
DB_NAME="database_name"

echo "🗃️ بدء عملية هجرة قاعدة البيانات..."

# 1. نقل ملفات الهجرة
echo "📁 نقل ملفات الهجرة..."
scp database/migrations/2024_01_15_000001_create_receipt_orders_table.php $SERVER_USER@$SERVER_HOST:$PROJECT_PATH/database/migrations/
scp database/migrations/2024_01_15_000002_create_receipt_order_products_table.php $SERVER_USER@$SERVER_HOST:$PROJECT_PATH/database/migrations/
scp run_migrations.sql $SERVER_USER@$SERVER_HOST:$PROJECT_PATH/

# 2. تشغيل الهجرة
echo "⚡ تشغيل الهجرة..."
ssh $SERVER_USER@$SERVER_HOST "cd $PROJECT_PATH && php artisan migrate"

# 3. التحقق من النجاح
echo "✅ التحقق من نجاح الهجرة..."
ssh $SERVER_USER@$SERVER_HOST "cd $PROJECT_PATH && php artisan migrate:status | grep receipt"

echo "🎉 تمت الهجرة بنجاح!"
echo "🧪 يمكنك الآن اختبار الصفحات الجديدة"
```

## 🎉 **النتيجة المتوقعة**

بعد تشغيل الهجرة بنجاح:

### **الجداول المنشأة:**
- ✅ **receipt_orders** - جدول أوامر الاستلام الرئيسي
- ✅ **receipt_order_products** - جدول منتجات كل أمر

### **العلاقات المنشأة:**
- ✅ **receipt_orders → warehouses** (المستودع الهدف)
- ✅ **receipt_orders → warehouses** (المستودع المصدر)
- ✅ **receipt_orders → venders** (المورد)
- ✅ **receipt_orders → users** (المستخدم المنشئ)
- ✅ **receipt_order_products → receipt_orders** (الأمر)
- ✅ **receipt_order_products → product_services** (المنتج)

### **الفهارس المنشأة:**
- ✅ **فهارس الأداء** لتسريع الاستعلامات
- ✅ **فهارس البحث** للفلترة والترتيب
- ✅ **فهارس العلاقات** للربط بين الجداول

الآن قاعدة البيانات جاهزة لاستقبال أوامر الاستلام! 🚀✨
