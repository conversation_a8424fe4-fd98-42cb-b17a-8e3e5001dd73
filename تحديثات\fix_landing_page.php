<?php
// إصلاح إعدادات صفحة الهبوط
require_once 'vendor/autoload.php';
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use Illuminate\Support\Facades\DB;

echo "<h1>🔧 إصلاح إعدادات صفحة الهبوط</h1>";
echo "<hr>";

try {
    // تحديث إعداد صفحة الهبوط
    $updated = DB::table('settings')
        ->where('name', 'display_landing_page')
        ->update(['value' => 'off']); // تعطيل صفحة الهبوط للتوجيه لصفحة تسجيل الدخول
    
    if ($updated) {
        echo "<p style='color: green;'>✅ تم تعطيل صفحة الهبوط بنجاح - سيتم التوجيه لصفحة تسجيل الدخول</p>";
    } else {
        // إذا لم يكن الإعداد موجود، أنشئه
        DB::table('settings')->insert([
            'name' => 'display_landing_page',
            'value' => 'off',
            'created_by' => 1,
            'created_at' => now(),
            'updated_at' => now()
        ]);
        echo "<p style='color: green;'>✅ تم إنشاء إعداد صفحة الهبوط وتعطيلها</p>";
    }
    
    echo "<p><strong>الآن جرب الرابط:</strong> <a href='http://localhost/up20251'>http://localhost/up20251</a></p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ: " . $e->getMessage() . "</p>";
    
    // حل بديل - إنشاء ملف index.php يوجه لصفحة تسجيل الدخول
    echo "<h2>🔄 حل بديل:</h2>";
    echo "<p>سيتم إنشاء ملف index.php يوجه مباشرة لصفحة تسجيل الدخول</p>";
}
?>
