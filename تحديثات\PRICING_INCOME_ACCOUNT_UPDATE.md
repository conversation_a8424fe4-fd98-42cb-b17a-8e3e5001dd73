# تحديث صفحة التسعير - إضافة عمود حساب الإيرادات

## 📋 التحديث المطلوب

تم إضافة عمود **"Income Account"** (حساب الإيرادات) إلى صفحة التسعير مع إمكانية التعديل المباشر.

## ✅ التحديثات المنجزة

### 1. Controller (PricingController.php)

#### أ. تحديث قائمة الحقول المسموحة
```php
// في دالة updateInline()
'field' => 'required|string|in:name,sku,sale_price,purchase_price,expense_chartaccount_id,sale_chartaccount_id,category_id,unit_id,type,tax_id'
```

#### ب. إضافة قواعد التحقق
```php
$fieldRules = [
    // ... الحقول الأخرى
    'sale_chartaccount_id' => 'required|integer|exists:chart_of_accounts,id',
    // ...
];
```

#### ج. تحديث دالة getFieldOptions()
```php
case 'expense_chartaccount_id':
case 'sale_chartaccount_id':
    $accounts = ChartOfAccount::where('created_by', '=', Auth::user()->creatorId())
        ->get();
    // ...
```

#### د. تحديث دالة formatDisplayValue()
```php
case 'sale_chartaccount_id':
    $account = ChartOfAccount::find($product->sale_chartaccount_id);
    return $account ? $account->name : '-';
```

### 2. View (pricing/index.blade.php)

#### أ. إضافة عمود في header الجدول
```html
<th>{{ __('حساب الإيرادات') }}</th>
<th>{{ __('حساب المصروفات') }}</th>
```

#### ب. إضافة خلية في body الجدول
```html
<!-- حساب الإيرادات -->
<td class="editable" data-field="sale_chartaccount_id" data-type="select">
    @php
        $incomeAccount = \App\Models\ChartOfAccount::find($product->sale_chartaccount_id);
    @endphp
    {{ $incomeAccount ? $incomeAccount->name : '-' }}
</td>
```

#### ج. تحديث DataTable configuration
```javascript
"columnDefs": [
    { "orderable": false, "targets": [0, 12] } // تحديث الفهرس بعد إضافة العمود
]
```

## 🎯 الميزات الجديدة

### 1. عرض حساب الإيرادات
- يظهر اسم حساب الإيرادات المرتبط بالمنتج
- يعرض "-" إذا لم يكن هناك حساب محدد

### 2. التعديل المباشر
- نقرة واحدة على الخلية لتفعيل التعديل
- قائمة منسدلة تحتوي على جميع حسابات الشركة
- حفظ تلقائي عند الاختيار

### 3. التحقق من الصحة
- التأكد من وجود الحساب في قاعدة البيانات
- التأكد من أن الحساب ينتمي للشركة الحالية

## 📊 ترتيب الأعمدة الجديد

| # | العمود | قابل للتعديل | النوع |
|---|--------|-------------|-------|
| 1 | الصورة | ❌ | عرض |
| 2 | الاسم | ✅ | نص |
| 3 | SKU | ✅ | نص |
| 4 | سعر البيع | ✅ | رقم |
| 5 | سعر الشراء | ✅ | رقم |
| 6 | **حساب الإيرادات** | ✅ | قائمة |
| 7 | حساب المصروفات | ✅ | قائمة |
| 8 | الضريبة | ✅ | قائمة |
| 9 | الفئة | ✅ | قائمة |
| 10 | الوحدة | ✅ | قائمة |
| 11 | النوع | ✅ | قائمة |
| 12 | الكمية | ❌ | عرض |
| 13 | الإجراءات | ❌ | أزرار |

## 🔧 التفاصيل التقنية

### قاعدة البيانات
- الحقل: `sale_chartaccount_id` في جدول `product_services`
- النوع: `integer`
- العلاقة: Foreign Key مع جدول `chart_of_accounts`

### العلاقة في Model
```php
// في ProductService Model
public function saleAccount()
{
    return $this->belongsTo(ChartOfAccount::class, 'sale_chartaccount_id');
}
```

### AJAX Request
```javascript
// عند التعديل المباشر
$.post('/pricing/update-inline', {
    _token: '{{ csrf_token() }}',
    id: productId,
    field: 'sale_chartaccount_id',
    value: selectedAccountId
})
```

## 🚀 خطوات النشر

### الملفات المحدثة
```
app/Http/Controllers/PricingController.php
resources/views/pricing/index.blade.php
```

### التحديثات المطلوبة
1. رفع الملفين المحدثين
2. لا حاجة لتحديث قاعدة البيانات (الحقل موجود مسبقاً)
3. لا حاجة لتحديث Routes أو Models

## 🧪 اختبار التحديث

### 1. اختبار العرض
- تأكد من ظهور عمود "حساب الإيرادات"
- تأكد من عرض أسماء الحسابات بشكل صحيح

### 2. اختبار التعديل المباشر
- انقر على خلية حساب الإيرادات
- تأكد من ظهور قائمة الحسابات
- اختر حساب جديد وتأكد من الحفظ

### 3. اختبار التحقق من الصحة
- تأكد من عدم إمكانية اختيار حسابات من شركات أخرى
- تأكد من ظهور رسائل خطأ مناسبة

## 📈 الفوائد

### 1. إدارة محاسبية أفضل
- ربط كل منتج بحساب إيرادات محدد
- تسهيل إعداد التقارير المالية
- تحسين دقة المحاسبة

### 2. سهولة الاستخدام
- تعديل سريع ومباشر
- واجهة موحدة مع باقي الحقول
- لا حاجة لفتح صفحات منفصلة

### 3. الموثوقية
- التحقق من صحة البيانات
- حماية من الأخطاء
- تسجيل جميع التغييرات

## ✅ النتيجة النهائية

تم إضافة عمود **"حساب الإيرادات"** بنجاح مع:
- ✅ عرض واضح في الجدول
- ✅ تعديل مباشر سهل
- ✅ تحقق من صحة البيانات
- ✅ تكامل مع النظام الحالي

**الصفحة جاهزة للاستخدام مع العمود الجديد! 🎉**
