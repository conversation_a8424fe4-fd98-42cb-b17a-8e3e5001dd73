-- SQL script to add the show_in_pos column to the product_service_categories table
-- Run this script directly in your database if the migration doesn't work

-- Check if the column exists first
SET @column_exists = 0;
SELECT COUNT(*) INTO @column_exists 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'product_service_categories' 
AND COLUMN_NAME = 'show_in_pos';

-- Only add the column if it doesn't exist
SET @sql = IF(@column_exists = 0, 
    'ALTER TABLE `product_service_categories` ADD COLUMN `show_in_pos` TINYINT(1) NOT NULL DEFAULT 1 AFTER `color`', 
    'SELECT "Column already exists, no action taken"');

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Update all existing product & service categories to show in POS by default
UPDATE `product_service_categories` 
SET `show_in_pos` = 1 
WHERE `type` = 'product & service';

-- Update all non-product categories to not show in POS
UPDATE `product_service_categories` 
SET `show_in_pos` = 0 
WHERE `type` != 'product & service';
