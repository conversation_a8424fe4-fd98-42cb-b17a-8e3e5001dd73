# قائمة فحص نشر الملفات على السيرفر

## ✅ 1. الملفات الأساسية المطلوب رفعها

### Controllers
- [ ] `app/Http/Controllers/FinancialRecordController.php`
- [ ] تحديث `app/Http/Controllers/BranchCashManagementController.php`

### Models
- [ ] `app/Models/Shift.php`
- [ ] `app/Models/FinancialRecord.php`
- [ ] `app/Models/DeliveryFinancialRecord.php`
- [ ] `app/Models/FinancialTransactions.php`
- [ ] تحديث `app/Models/User.php` (إضافة is_sale_session_new للـ fillable)

### Services
- [ ] `app/Services/FinancialRecordService.php`
- [ ] `app/Services/FinancialTransactionService.php`

### Views
- [ ] `resources/views/pos/financial_record/opening-balance.blade.php`
- [ ] `resources/views/pos/financial_record/index.blade.php`
- [ ] `resources/views/pos/financial_record/index_delivery.blade.php`
- [ ] تحديث `resources/views/partials/admin/menu.blade.php`

### Routes
- [ ] تحديث `routes/web.php` (إضافة الـ routes الجديدة)

### Database Migrations
- [ ] `database/migrations/2025_03_08_192200_create_shifts_table.php`
- [ ] `database/migrations/2025_03_09_131457_create_financial_records_table.php`
- [ ] `database/migrations/2025_03_24_152432_create_delivery_financial_records_table.php`
- [ ] `database/migrations/2025_03_26_205047_create_financial_transactions_table.php`
- [ ] `database/migrations/2025_03_09_184355_add_is_sale_session_new_to_users_table.php`

## ✅ 2. أوامر السيرفر المطلوبة

### تشغيل Migrations
```bash
php artisan migrate
```

### مسح Cache
```bash
php artisan config:cache
php artisan route:cache
php artisan view:cache
php artisan cache:clear
```

### تحديث Composer (إذا لزم الأمر)
```bash
composer dump-autoload
```

## ✅ 3. فحص قاعدة البيانات

### التحقق من الجداول
```sql
-- فحص جدول users
DESCRIBE users;

-- فحص وجود حقل is_sale_session_new
SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'users' AND COLUMN_NAME = 'is_sale_session_new';

-- فحص الجداول الجديدة
SHOW TABLES LIKE 'shifts';
SHOW TABLES LIKE 'financial_records';
```

### تحديث بيانات المستخدمين
```sql
-- تعيين is_sale_session_new للمستخدمين الموجودين
UPDATE users SET is_sale_session_new = 1 WHERE warehouse_id IS NOT NULL;
```

## ✅ 4. فحص الصلاحيات

### التحقق من الصلاحيات في قاعدة البيانات
```sql
-- فحص صلاحيات المستخدم
SELECT u.name, u.warehouse_id, u.is_sale_session_new, p.name as permission
FROM users u
LEFT JOIN model_has_permissions mhp ON u.id = mhp.model_id
LEFT JOIN permissions p ON mhp.permission_id = p.id
WHERE u.id = [USER_ID] AND p.name IN ('manage pos', 'show financial record');
```

## ✅ 5. فحص الأخطاء

### فحص Laravel Logs
```bash
tail -f storage/logs/laravel.log
```

### فحص Web Server Logs
```bash
# Apache
tail -f /var/log/apache2/error.log

# Nginx
tail -f /var/log/nginx/error.log
```

## ✅ 6. اختبار الوظائف

### اختبار Route التشخيص
```
GET /debug-opening-balance
```

### اختبار AJAX Request
```javascript
// في Developer Tools Console
$.get('/pos-financial-record/opening-balance', function(data) {
    console.log(data);
});
```

## ✅ 7. إعدادات السيرفر

### التحقق من PHP Extensions
```bash
php -m | grep -E "(pdo|mysql|mbstring|openssl|tokenizer|xml|ctype|json)"
```

### التحقق من صلاحيات الملفات
```bash
# صلاحيات المجلدات
chmod 755 storage/
chmod 755 bootstrap/cache/
chmod 755 resources/views/pos/financial_record/

# صلاحيات الملفات
chmod 644 resources/views/pos/financial_record/opening-balance.blade.php
```

## ✅ 8. خطوات التشخيص السريع

1. **افتح Developer Tools في المتصفح**
2. **اذهب إلى Network Tab**
3. **انقر على "Add POS"**
4. **ابحث عن طلب AJAX إلى `/pos-financial-record/opening-balance`**
5. **تحقق من:**
   - Status Code (يجب أن يكون 200)
   - Response Content
   - أي رسائل خطأ

## ✅ 9. الحلول الشائعة

### إذا كان الخطأ 404
- تحقق من وجود Route في web.php
- تشغيل `php artisan route:cache`

### إذا كان الخطأ 500
- فحص Laravel logs
- التحقق من وجود Controller و Method

### إذا كان الخطأ 403
- فحص صلاحيات المستخدم
- التحقق من قيمة is_sale_session_new

### إذا كان Response فارغ
- التحقق من وجود View file
- فحص syntax errors في View
