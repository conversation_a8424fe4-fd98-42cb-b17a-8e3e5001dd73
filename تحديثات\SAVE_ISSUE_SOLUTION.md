# حل مشكلة عدم حفظ أمر الاستلام

## 🔍 المشكلة
عند النقر على زر "حفظ أمر الاستلام" لا يتم حفظ البيانات.

## 🛠️ خطوات التشخيص والحل

### الخطوة 1: فحص قاعدة البيانات

#### أ) رفع ملف التشخيص:
```bash
scp debug_save_issue.php user@server:/path/to/public/
```

#### ب) تعديل إعدادات قاعدة البيانات:
```php
$host = 'localhost';
$dbname = 'your_database_name';
$username = 'your_username';
$password = 'your_password';
```

#### ج) تشغيل التشخيص:
```
http://yoursite.com/debug_save_issue.php
```

### الخطوة 2: إنشاء الجداول المطلوبة

إذا لم تكن الجداول موجودة، قم بتشغيل هذه الأوامر:

#### أ) جدول أوامر الاستلام:
```sql
CREATE TABLE `receipt_orders` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `order_number` varchar(255) NOT NULL,
    `order_type` enum('استلام بضاعة','نقل بضاعة','أمر إخراج') NOT NULL,
    `vendor_id` bigint(20) UNSIGNED DEFAULT NULL,
    `warehouse_id` bigint(20) UNSIGNED NOT NULL,
    `from_warehouse_id` bigint(20) UNSIGNED DEFAULT NULL,
    `invoice_number` varchar(255) DEFAULT NULL,
    `invoice_total` decimal(15,2) DEFAULT NULL,
    `invoice_date` date DEFAULT NULL,
    `has_return` tinyint(1) NOT NULL DEFAULT 0,
    `total_products` int(11) NOT NULL DEFAULT 0,
    `total_amount` decimal(15,2) NOT NULL DEFAULT 0.00,
    `notes` text DEFAULT NULL,
    `exit_reason` enum('فقدان','منتهي الصلاحية','تلف/خراب','بيع بالتجزئة') DEFAULT NULL,
    `exit_date` date DEFAULT NULL,
    `responsible_person` varchar(255) DEFAULT NULL,
    `created_by` bigint(20) UNSIGNED NOT NULL,
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `receipt_orders_order_number_unique` (`order_number`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

#### ب) جدول منتجات أوامر الاستلام:
```sql
CREATE TABLE `receipt_order_products` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `receipt_order_id` bigint(20) UNSIGNED NOT NULL,
    `product_id` bigint(20) UNSIGNED NOT NULL,
    `quantity` decimal(15,2) NOT NULL,
    `unit_cost` decimal(15,2) NOT NULL DEFAULT 0.00,
    `total_cost` decimal(15,2) NOT NULL DEFAULT 0.00,
    `expiry_date` date DEFAULT NULL,
    `is_return` tinyint(1) NOT NULL DEFAULT 0,
    `notes` text DEFAULT NULL,
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `receipt_order_products_receipt_order_id_index` (`receipt_order_id`),
    KEY `receipt_order_products_product_id_index` (`product_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

### الخطوة 3: فحص JavaScript

#### أ) افتح Developer Tools (F12)
#### ب) اذهب إلى Console
#### ج) جرب حفظ أمر استلام وراقب الرسائل

**الرسائل المتوقعة:**
```
🚀 Form submission started...
📋 Form data check:
- Order Type: استلام بضاعة
- Warehouse ID: 1
- Products Count: 1
- Product 1: ID=1, Quantity=5
✅ All validations passed, submitting form...
```

**إذا ظهرت رسائل خطأ:**
- `يجب اختيار نوع الأمر`
- `يجب اختيار المستودع`
- `يجب إضافة منتج واحد على الأقل`
- `يجب اختيار منتج وإدخال كمية صحيحة`

### الخطوة 4: فحص Laravel Logs

```bash
# فحص ملف اللوج
tail -f storage/logs/laravel.log

# أو عرض آخر 50 سطر
tail -50 storage/logs/laravel.log
```

**الأخطاء الشائعة:**
- `Table 'database.receipt_orders' doesn't exist`
- `SQLSTATE[42S02]: Base table or view not found`
- `Class 'App\Models\ReceiptOrder' not found`

### الخطوة 5: فحص الصلاحيات

```sql
-- فحص صلاحيات المستخدم
SELECT p.name as permission_name 
FROM permissions p
JOIN model_has_permissions mhp ON p.id = mhp.permission_id
WHERE mhp.model_id = YOUR_USER_ID AND mhp.model_type = 'App\\Models\\User';
```

**الصلاحيات المطلوبة:**
- `manage warehouse`

### الخطوة 6: مسح الكاش

```bash
ssh user@server "cd /path/to/project && php artisan cache:clear"
ssh user@server "cd /path/to/project && php artisan config:clear"
ssh user@server "cd /path/to/project && php artisan route:clear"
ssh user@server "cd /path/to/project && php artisan view:clear"
```

## 🔧 الحلول حسب نوع المشكلة

### الحل 1: الجداول غير موجودة

#### أ) استخدام Laravel Migration:
```bash
ssh user@server "cd /path/to/project && php artisan migrate"
```

#### ب) أو تشغيل SQL مباشرة:
```bash
mysql -u username -p database_name < run_migrations.sql
```

### الحل 2: النماذج غير موجودة

#### تأكد من وجود الملفات:
```bash
ls -la app/Models/ReceiptOrder.php
ls -la app/Models/ReceiptOrderProduct.php
```

#### إذا لم تكن موجودة، ارفعها:
```bash
scp app/Models/ReceiptOrder.php user@server:/path/to/project/app/Models/
scp app/Models/ReceiptOrderProduct.php user@server:/path/to/project/app/Models/
```

### الحل 3: مشكلة في Validation

#### فحص البيانات المرسلة:
```javascript
// في Console قبل الإرسال
$('form').on('submit', function() {
    console.log('Form Data:', $(this).serialize());
});
```

#### تعديل Validation في الكونترولر:
```php
// إضافة تشخيص
\Log::info('Form submitted', $request->all());
```

### الحل 4: مشكلة CSRF Token

#### التحقق من وجود Token:
```html
<!-- في head -->
<meta name="csrf-token" content="{{ csrf_token() }}">

<!-- في النموذج -->
@csrf
```

### الحل 5: مشكلة في المسارات

#### فحص المسارات:
```bash
php artisan route:list | grep receipt
```

#### التأكد من وجود:
- `receipt-order.store`

## 📋 قائمة التحقق السريعة

- [ ] **الجداول موجودة**: `receipt_orders`, `receipt_order_products`
- [ ] **النماذج موجودة**: `ReceiptOrder.php`, `ReceiptOrderProduct.php`
- [ ] **المسارات تعمل**: `receipt-order.store`
- [ ] **الصلاحيات صحيحة**: `manage warehouse`
- [ ] **JavaScript لا يظهر أخطاء**: فحص Console
- [ ] **CSRF Token موجود**: `@csrf` في النموذج
- [ ] **البيانات صحيحة**: نوع الأمر، مستودع، منتجات

## 🧪 اختبار سريع

### اختبار 1: إنشاء أمر بسيط
```
1. اختر "استلام بضاعة"
2. اختر مستودع
3. أضف منتج واحد بكمية 1
4. اضغط حفظ
5. راقب Console و Laravel Log
```

### اختبار 2: فحص قاعدة البيانات
```sql
-- بعد محاولة الحفظ
SELECT * FROM receipt_orders ORDER BY id DESC LIMIT 1;
SELECT * FROM receipt_order_products ORDER BY id DESC LIMIT 1;
```

## 🚨 الأخطاء الشائعة والحلول

### خطأ: "Table doesn't exist"
```bash
# الحل: إنشاء الجداول
php artisan migrate
# أو
mysql -u username -p database_name < run_migrations.sql
```

### خطأ: "Class not found"
```bash
# الحل: رفع النماذج ومسح الكاش
scp app/Models/*.php user@server:/path/to/project/app/Models/
php artisan cache:clear
```

### خطأ: "Permission denied"
```sql
-- الحل: إعطاء صلاحيات
INSERT INTO model_has_permissions (permission_id, model_type, model_id)
SELECT p.id, 'App\\Models\\User', YOUR_USER_ID
FROM permissions p WHERE p.name = 'manage warehouse';
```

### خطأ: "Validation failed"
```
الحل: تحقق من:
- اختيار نوع الأمر
- اختيار المستودع
- إضافة منتج واحد على الأقل
- إدخال كمية صحيحة
```

## 📞 طلب المساعدة

إذا استمرت المشكلة، شارك:

1. **نتائج `debug_save_issue.php`**
2. **رسائل Console من المتصفح**
3. **محتوى Laravel Log**
4. **نتائج فحص الجداول**
5. **صلاحيات المستخدم**

## ✅ النتيجة المتوقعة

بعد تطبيق الحلول:
- ✅ **حفظ ناجح** مع رسالة "تم إنشاء الأمر بنجاح"
- ✅ **إعادة توجيه** لصفحة قائمة الأوامر
- ✅ **ظهور الأمر** في القائمة
- ✅ **تحديث المخزون** تلقائياً
