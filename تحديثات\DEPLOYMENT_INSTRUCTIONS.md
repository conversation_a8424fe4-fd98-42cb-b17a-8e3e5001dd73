# 🚀 تعليمات النشر - معالجة فواتير المستودع

## 📋 قائمة المراجعة قبل النشر

- [ ] عمل نسخة احتياطية من الملفات الحالية
- [ ] التأكد من عمل الميزة في بيئة التطوير
- [ ] تحضير الملفات للنقل
- [ ] التحقق من صلاحيات الوصول للسيرفر

## 📁 الملفات المطلوب نقلها

### 1. الكونترولر الجديد
```
المصدر: app/Http/Controllers/WarehousePurchaseProcessingController.php
الهدف: app/Http/Controllers/WarehousePurchaseProcessingController.php
```

### 2. ملفات العرض
```
المصدر: resources/views/warehouse_purchase_processing/
الهدف: resources/views/warehouse_purchase_processing/
```

### 3. تحديث المسارات
```
الملف: routes/web.php
الإجراء: إضافة المسارات الجديدة
```

## 🔧 خطوات النشر التفصيلية

### الخطوة 1: النسخ الاحتياطي
```bash
# في السيرفر
cd /path/to/your/laravel/project

# نسخ احتياطي للمسارات
cp routes/web.php routes/web.php.backup.$(date +%Y%m%d_%H%M%S)

# نسخ احتياطي للكونترولرات (إذا كان موجود)
if [ -f "app/Http/Controllers/WarehousePurchaseProcessingController.php" ]; then
    cp app/Http/Controllers/WarehousePurchaseProcessingController.php app/Http/Controllers/WarehousePurchaseProcessingController.php.backup
fi
```

### الخطوة 2: رفع الملفات

#### أ. رفع الكونترولر
```bash
# نسخ الكونترولر الجديد
scp WarehousePurchaseProcessingController.php user@server:/path/to/project/app/Http/Controllers/
```

#### ب. رفع ملفات العرض
```bash
# إنشاء المجلد إذا لم يكن موجود
ssh user@server "mkdir -p /path/to/project/resources/views/warehouse_purchase_processing"

# نسخ ملفات العرض
scp index.blade.php user@server:/path/to/project/resources/views/warehouse_purchase_processing/
scp edit_products.blade.php user@server:/path/to/project/resources/views/warehouse_purchase_processing/
```

### الخطوة 3: تحديث المسارات

أضف هذه المسارات إلى ملف `routes/web.php`:

```php
// إضافة في نهاية ملف web.php قبل الإغلاق

// Warehouse Purchase Processing Routes
Route::get('warehouse-purchase-processing', [App\Http\Controllers\WarehousePurchaseProcessingController::class, 'index'])
    ->name('warehouse.purchase.processing.index')
    ->middleware(['auth', 'XSS']);

Route::post('warehouse-purchase-processing/update-inline', [App\Http\Controllers\WarehousePurchaseProcessingController::class, 'updateInline'])
    ->name('warehouse.purchase.processing.update.inline')
    ->middleware(['auth', 'XSS']);

Route::get('warehouse-purchase-processing/field-options', [App\Http\Controllers\WarehousePurchaseProcessingController::class, 'getFieldOptions'])
    ->name('warehouse.purchase.processing.field.options')
    ->middleware(['auth', 'XSS']);

Route::get('warehouse-purchase-processing/{id}/edit-products', [App\Http\Controllers\WarehousePurchaseProcessingController::class, 'editProducts'])
    ->name('warehouse.purchase.processing.edit.products')
    ->middleware(['auth', 'XSS']);

Route::post('warehouse-purchase-processing/update-product', [App\Http\Controllers\WarehousePurchaseProcessingController::class, 'updateProduct'])
    ->name('warehouse.purchase.processing.update.product')
    ->middleware(['auth', 'XSS']);

Route::post('warehouse-purchase-processing/add-product', [App\Http\Controllers\WarehousePurchaseProcessingController::class, 'addProduct'])
    ->name('warehouse.purchase.processing.add.product')
    ->middleware(['auth', 'XSS']);

Route::delete('warehouse-purchase-processing/delete-product', [App\Http\Controllers\WarehousePurchaseProcessingController::class, 'deleteProduct'])
    ->name('warehouse.purchase.processing.delete.product')
    ->middleware(['auth', 'XSS']);
```

### الخطوة 4: تحديث الذاكرة المؤقتة
```bash
# في السيرفر
cd /path/to/your/laravel/project

# مسح الذاكرة المؤقتة
php artisan route:clear
php artisan config:clear
php artisan cache:clear
php artisan view:clear

# إعادة تحميل الذاكرة المؤقتة
php artisan route:cache
php artisan config:cache
```

### الخطوة 5: ضبط الصلاحيات
```bash
# ضبط صلاحيات الملفات
chmod 644 app/Http/Controllers/WarehousePurchaseProcessingController.php
chmod 644 resources/views/warehouse_purchase_processing/*.blade.php

# ضبط صلاحيات المجلدات
chmod 755 resources/views/warehouse_purchase_processing/
```

## ✅ التحقق من النشر

### 1. فحص المسارات
```bash
php artisan route:list | grep warehouse-purchase-processing
```

**النتيجة المتوقعة:**
```
GET|HEAD  warehouse-purchase-processing                    warehouse.purchase.processing.index
POST      warehouse-purchase-processing/update-inline     warehouse.purchase.processing.update.inline
GET|HEAD  warehouse-purchase-processing/field-options     warehouse.purchase.processing.field.options
GET|HEAD  warehouse-purchase-processing/{id}/edit-products warehouse.purchase.processing.edit.products
POST      warehouse-purchase-processing/update-product    warehouse.purchase.processing.update.product
POST      warehouse-purchase-processing/add-product       warehouse.purchase.processing.add.product
DELETE    warehouse-purchase-processing/delete-product    warehouse.purchase.processing.delete.product
```

### 2. فحص الملفات
```bash
# تحقق من وجود الكونترولر
ls -la app/Http/Controllers/WarehousePurchaseProcessingController.php

# تحقق من ملفات العرض
ls -la resources/views/warehouse_purchase_processing/
```

### 3. اختبار الوصول
```bash
# اختبار الصفحة الرئيسية
curl -I https://yoursite.com/warehouse-purchase-processing

# اختبار API للخيارات
curl "https://yoursite.com/warehouse-purchase-processing/field-options?field=status"
```

## 🎯 اختبار الوظائف

### 1. اختبار الصفحة الرئيسية
- افتح: `https://yoursite.com/warehouse-purchase-processing`
- تحقق من ظهور الجدول
- تحقق من وجود البيانات

### 2. اختبار التعديل المباشر
- انقر على حقل المورد
- تحقق من ظهور القائمة المنسدلة
- جرب الحفظ والإلغاء

### 3. اختبار تعديل المنتجات
- انقر على زر تعديل المنتجات
- تحقق من فتح الصفحة الجديدة

## 🚨 استكشاف الأخطاء

### خطأ 404 - الصفحة غير موجودة
```bash
# تحقق من المسارات
php artisan route:list | grep warehouse

# مسح ذاكرة المسارات
php artisan route:clear
php artisan route:cache
```

### خطأ 500 - خطأ في الخادم
```bash
# فحص سجل الأخطاء
tail -f storage/logs/laravel.log

# تحقق من صلاحيات الملفات
ls -la app/Http/Controllers/WarehousePurchaseProcessingController.php
```

### خطأ في التعديل المباشر
- افتح F12 في المتصفح
- تحقق من رسائل Console
- تحقق من طلبات Network

## 📞 الدعم الفني

### معلومات مطلوبة عند طلب الدعم:
1. رسائل الخطأ من `storage/logs/laravel.log`
2. رسائل Console من المتصفح
3. إصدار PHP و Laravel
4. تفاصيل السيرفر

### أدوات التشخيص:
- استخدم صفحة التشخيص: `/debug_inline_editing.html`
- راجع ملف: `INLINE_EDITING_TROUBLESHOOTING.md`

---

## ✅ تم النشر بنجاح!

بعد اتباع هذه الخطوات، ستكون ميزة التعديل المباشر متاحة في موقعك! 🎉
