#!/bin/bash

# سكريبت حل مشكلة الهجرة المعلقة
# يحل مشكلة receipt_order_products table في حالة Pending

# ألوان للعرض
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# متغيرات - عدل هذه القيم حسب خادمك
SERVER_USER="your_username"
SERVER_HOST="your_server_ip"
PROJECT_PATH="/path/to/your/project"
DB_USER="database_username"
DB_NAME="database_name"

# دالة لطباعة الرسائل الملونة
print_status() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] ⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] ℹ️  $1${NC}"
}

echo -e "${GREEN}"
echo "╔══════════════════════════════════════════════════════════════╗"
echo "║                   حل مشكلة الهجرة المعلقة                     ║"
echo "║                Fix Pending Migration Issue                  ║"
echo "╚══════════════════════════════════════════════════════════════╝"
echo -e "${NC}"

print_info "بدء عملية حل مشكلة الهجرة المعلقة..."

# الخطوة 1: فحص حالة الهجرة الحالية
print_status "🔍 الخطوة 1: فحص حالة الهجرة..."
ssh $SERVER_USER@$SERVER_HOST "cd $PROJECT_PATH && php artisan migrate:status | grep receipt"

# الخطوة 2: نقل ملف SQL الإصلاحي
print_status "📁 الخطوة 2: نقل ملف الإصلاح..."
scp fix_pending_migration.sql $SERVER_USER@$SERVER_HOST:$PROJECT_PATH/

# الخطوة 3: محاولة تشغيل الهجرة بالقوة
print_status "⚡ الخطوة 3: محاولة تشغيل الهجرة بالقوة..."
ssh $SERVER_USER@$SERVER_HOST "cd $PROJECT_PATH && php artisan migrate --force"

if [ $? -eq 0 ]; then
    print_status "✅ نجحت الهجرة بالقوة!"
    
    # التحقق من الحالة
    print_info "التحقق من حالة الهجرة..."
    ssh $SERVER_USER@$SERVER_HOST "cd $PROJECT_PATH && php artisan migrate:status | grep receipt"
    
    print_status "🎉 تم حل المشكلة بنجاح!"
    exit 0
else
    print_warning "فشلت الهجرة بالقوة، جاري المحاولة بالطريقة اليدوية..."
fi

# الخطوة 4: الحل اليدوي باستخدام SQL
print_status "🔧 الخطوة 4: تطبيق الحل اليدوي..."
ssh $SERVER_USER@$SERVER_HOST "cd $PROJECT_PATH && mysql -u $DB_USER -p $DB_NAME < fix_pending_migration.sql"

if [ $? -eq 0 ]; then
    print_status "✅ نجح الحل اليدوي!"
else
    print_error "فشل الحل اليدوي، جاري المحاولة بطريقة أخرى..."
    
    # الخطوة 5: حل بديل - إنشاء الجدول بأبسط شكل
    print_status "🔧 الخطوة 5: الحل البديل..."
    ssh $SERVER_USER@$SERVER_HOST "cd $PROJECT_PATH && mysql -u $DB_USER -p $DB_NAME -e '
    DROP TABLE IF EXISTS receipt_order_products;
    CREATE TABLE receipt_order_products (
        id bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
        receipt_order_id bigint(20) UNSIGNED NOT NULL,
        product_id bigint(20) UNSIGNED NOT NULL,
        quantity decimal(15,2) NOT NULL,
        unit_cost decimal(15,2) DEFAULT 0.00,
        total_cost decimal(15,2) DEFAULT 0.00,
        expiry_date date DEFAULT NULL,
        is_return tinyint(1) DEFAULT 0,
        notes text DEFAULT NULL,
        created_at timestamp NULL DEFAULT NULL,
        updated_at timestamp NULL DEFAULT NULL,
        PRIMARY KEY (id)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;'"
    
    if [ $? -eq 0 ]; then
        print_status "✅ نجح الحل البديل!"
        
        # تحديث جدول migrations
        print_info "تحديث جدول migrations..."
        ssh $SERVER_USER@$SERVER_HOST "cd $PROJECT_PATH && mysql -u $DB_USER -p $DB_NAME -e \"
        INSERT IGNORE INTO migrations (migration, batch) 
        VALUES ('2024_01_15_000002_create_receipt_order_products_table', 
                (SELECT COALESCE(MAX(batch), 0) + 1 FROM (SELECT batch FROM migrations) as temp));
        \""
    else
        print_error "فشل جميع الحلول!"
        exit 1
    fi
fi

# الخطوة 6: التحقق النهائي
print_status "✅ الخطوة 6: التحقق النهائي..."

# فحص حالة الهجرة
print_info "فحص حالة الهجرة..."
ssh $SERVER_USER@$SERVER_HOST "cd $PROJECT_PATH && php artisan migrate:status | grep receipt"

# فحص وجود الجدول
print_info "فحص وجود الجدول..."
ssh $SERVER_USER@$SERVER_HOST "cd $PROJECT_PATH && mysql -u $DB_USER -p $DB_NAME -e 'SHOW TABLES LIKE \"receipt_%\";'"

# فحص هيكل الجدول
print_info "فحص هيكل الجدول..."
ssh $SERVER_USER@$SERVER_HOST "cd $PROJECT_PATH && mysql -u $DB_USER -p $DB_NAME -e 'DESCRIBE receipt_order_products;'"

# الخطوة 7: مسح الكاش وإعادة التحميل
print_status "🧹 الخطوة 7: مسح الكاش..."
ssh $SERVER_USER@$SERVER_HOST "cd $PROJECT_PATH && php artisan cache:clear && php artisan view:clear && php artisan route:clear && php artisan config:clear"

# الخطوة 8: اختبار الداشبورد
print_status "🧪 الخطوة 8: اختبار الوصول للداشبورد..."
print_info "جاري اختبار الوصول للداشبورد..."

# النتيجة النهائية
echo -e "${GREEN}"
echo "╔══════════════════════════════════════════════════════════════╗"
echo "║                    تم حل المشكلة بنجاح! 🎉                     ║"
echo "╚══════════════════════════════════════════════════════════════╝"
echo -e "${NC}"

print_status "🎯 تم حل مشكلة الهجرة المعلقة بنجاح!"
print_info "يمكنك الآن الوصول للداشبورد والصفحات الجديدة"
print_warning "تأكد من اختبار جميع الوظائف قبل الاستخدام الفعلي"

print_status "انتهى الإصلاح في $(date +'%Y-%m-%d %H:%M:%S')"
