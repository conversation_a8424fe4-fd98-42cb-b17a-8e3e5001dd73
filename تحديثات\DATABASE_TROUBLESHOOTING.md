# دليل حل مشاكل قاعدة البيانات

## المشكلة
لا يستطيع النظام إرسال البيانات إلى قاعدة البيانات عند إنشاء منتج جديد

## الأسباب المحتملة

### 1. مشاكل الاتصال
- إعدادات خاطئة في ملف `.env`
- خادم قاعدة البيانات متوقف
- مشاكل في الشبكة

### 2. مشاكل الجداول
- جداول مفقودة أو تالفة
- أعمدة مفقودة
- قيود خاطئة (constraints)

### 3. مشاكل الصلاحيات
- مستخدم قاعدة البيانات لا يملك صلاحيات كافية
- قيود على الكتابة

### 4. مشاكل المساحة
- امتلاء القرص الصلب
- حد أقصى لحجم قاعدة البيانات

## خطوات الحل

### الخطوة 1: فحص إعدادات .env
تحقق من ملف `.env` في جذر المشروع:

```env
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=ee
DB_USERNAME=root
DB_PASSWORD=
```

**تأكد من:**
- اسم قاعدة البيانات صحيح (`ee`)
- اسم المستخدم وكلمة المرور صحيحة
- الخادم يعمل على المنفذ الصحيح

### الخطوة 2: اختبار الاتصال
```bash
# في مجلد المشروع
php artisan tinker
```

ثم:
```php
DB::connection()->getPdo();
echo "Connection successful!";
```

### الخطوة 3: تشغيل ملف الإصلاح
1. افتح phpMyAdmin
2. اختر قاعدة البيانات `ee`
3. انسخ والصق محتوى ملف `fix_database_issues.sql`
4. شغل الأوامر

### الخطوة 4: تشغيل Migrations
```bash
php artisan migrate:status
php artisan migrate
php artisan migrate:refresh --seed
```

### الخطوة 5: فحص الصلاحيات
في phpMyAdmin، شغل:
```sql
SHOW GRANTS FOR 'root'@'localhost';
```

تأكد من وجود صلاحيات:
- SELECT
- INSERT
- UPDATE
- DELETE
- CREATE
- ALTER

### الخطوة 6: فحص مساحة القرص
```bash
# في Windows
dir C:\ 

# في Linux/Mac
df -h
```

### الخطوة 7: فحص ملفات السجلات
```bash
# Laravel logs
tail -f storage/logs/laravel.log

# MySQL logs (في Windows مع XAMPP)
tail -f C:\xampp\mysql\data\mysql_error.log
```

## الأخطاء الشائعة وحلولها

### "SQLSTATE[HY000] [2002] Connection refused"
**السبب:** خادم MySQL متوقف
**الحل:**
- ابدأ تشغيل XAMPP/WAMP
- تأكد من تشغيل خدمة MySQL

### "SQLSTATE[42S02]: Base table or view not found"
**السبب:** الجدول غير موجود
**الحل:**
```bash
php artisan migrate
```

### "SQLSTATE[23000]: Integrity constraint violation"
**السبب:** انتهاك قيود قاعدة البيانات
**الحل:**
- تحقق من البيانات المرجعية (foreign keys)
- تأكد من وجود الفئات والوحدات

### "SQLSTATE[42000]: Syntax error"
**السبب:** خطأ في صيغة SQL
**الحل:**
- تحقق من إصدار MySQL
- شغل ملف الإصلاح

### "Access denied for user"
**السبب:** مشكلة في الصلاحيات
**الحل:**
```sql
GRANT ALL PRIVILEGES ON ee.* TO 'root'@'localhost';
FLUSH PRIVILEGES;
```

## فحص سريع

### 1. اختبار الاتصال
```bash
php artisan tinker
include 'test_database_connection.php';
```

### 2. فحص الجداول
```sql
SHOW TABLES LIKE 'product%';
DESCRIBE product_services;
```

### 3. اختبار الإدراج
```sql
INSERT INTO product_services (name, sku, sale_price, purchase_price, type, category_id, unit_id, sale_chartaccount_id, expense_chartaccount_id, created_by, created_at, updated_at) 
VALUES ('اختبار', 'TEST123', 100, 80, 'product', 1, 1, 1, 1, 1, NOW(), NOW());
```

## إعادة تهيئة قاعدة البيانات (الحل الأخير)

إذا فشلت جميع الحلول:

```bash
# نسخ احتياطي أولاً
mysqldump -u root -p ee > backup.sql

# إعادة إنشاء قاعدة البيانات
php artisan migrate:fresh --seed

# أو يدوياً
DROP DATABASE ee;
CREATE DATABASE ee CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
php artisan migrate
```

## نصائح الوقاية

1. **نسخ احتياطية منتظمة**
2. **مراقبة مساحة القرص**
3. **تحديث إعدادات MySQL**
4. **فحص دوري للجداول**

## الدعم الإضافي

إذا استمرت المشكلة:
1. تحقق من إصدار PHP و MySQL
2. راجع ملفات error.log
3. جرب قاعدة بيانات جديدة
4. تأكد من إعدادات الخادم
