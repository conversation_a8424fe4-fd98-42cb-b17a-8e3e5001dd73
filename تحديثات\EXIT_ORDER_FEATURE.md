# ميزة أمر الإخراج - دليل شامل

## 🎯 نظرة عامة

تم إضافة ميزة **أمر الإخراج** لنظام أوامر الاستلام لتسجيل وإدارة إخراج المنتجات من المستودعات لأسباب مختلفة.

## 📋 حالات الإخراج المدعومة

### 1. **فقدان** 
- المنتجات المفقودة أو المسروقة
- لون الحالة: تحذيري (warning)

### 2. **منتهي الصلاحية**
- المنتجات منتهية الصلاحية
- يتم ربطها بتاريخ الصلاحية في النظام

### 3. **تلف/خراب**
- المنتجات التالفة أو المكسورة
- المنتجات غير صالحة للبيع

### 4. **بيع بالتجزئة**
- المنتجات المباعة بالتجزئة خارج النظام
- المبيعات المباشرة

## 🔧 الميزات المضافة

### **في واجهة المستخدم:**

#### **1. نوع الأمر الجديد:**
```html
<option value="أمر إخراج">أمر إخراج</option>
```

#### **2. حقول أمر الإخراج:**
- **حالة الإخراج**: قائمة منسدلة بالأسباب (مطلوب)
- **ملاحظات الإخراج**: نص حر للتفاصيل
- **تاريخ الإخراج**: تاريخ العملية (افتراضي: اليوم)
- **الشخص المسؤول**: اسم المسؤول عن الإخراج

#### **3. عرض ديناميكي:**
```javascript
// إظهار حقول أمر الإخراج عند الاختيار
if (orderType === 'أمر إخراج') {
    $('#exit_fields').show();
} else {
    $('#exit_fields').hide();
}
```

### **في قاعدة البيانات:**

#### **جدول `receipt_orders` - حقول جديدة:**
```sql
exit_reason ENUM('فقدان','منتهي الصلاحية','تلف/خراب','بيع بالتجزئة') NULL
exit_date DATE NULL
responsible_person VARCHAR(255) NULL
```

#### **جدول `receipt_order_products` - ملاحظات محسنة:**
```sql
notes TEXT -- يحتوي على: "إخراج - [السبب] - مسؤول: [الاسم]"
```

### **في الكونترولر:**

#### **1. Validation محسن:**
```php
// للتحقق من أمر الإخراج
if ($request->order_type === 'أمر إخراج') {
    $exitValidator = \Validator::make($request->all(), [
        'exit_reason' => 'required|in:فقدان,منتهي الصلاحية,تلف/خراب,بيع بالتجزئة',
        'exit_date' => 'required|date',
    ]);
}
```

#### **2. دالة معالجة جديدة:**
```php
private function processExitOrder($request, $user)
{
    // إنشاء أمر إخراج
    // خصم من المخزون
    // تسجيل السبب والمسؤول
    // ربط بتواريخ الصلاحية (إذا كان السبب انتهاء صلاحية)
}
```

## 🔄 تدفق العمل

### **1. إنشاء أمر إخراج:**
```
المستخدم يختار "أمر إخراج"
    ↓
تظهر حقول الإخراج
    ↓
المستخدم يختار المستودع
    ↓
المستخدم يختار حالة الإخراج
    ↓
المستخدم يضيف المنتجات والكميات
    ↓
النظام يخصم من المخزون
    ↓
حفظ الأمر مع التفاصيل
```

### **2. معالجة البيانات:**
```php
// إنشاء سجل الأمر
$receiptOrder = new ReceiptOrder();
$receiptOrder->order_type = 'أمر إخراج';
$receiptOrder->exit_reason = $request->exit_reason;
$receiptOrder->exit_date = $request->exit_date;
$receiptOrder->responsible_person = $request->responsible_person;

// معالجة كل منتج
foreach ($products as $product) {
    // خصم من المخزون
    $this->updateWarehouseStock($warehouse, $product, $quantity, 'subtract');
    
    // حفظ تفاصيل الإخراج
    $notes = 'إخراج - ' . $exit_reason . ' - مسؤول: ' . $responsible_person;
}
```

## 📊 التقارير والإحصائيات

### **في صفحة الفهرس:**
- **النوع**: "أمر إخراج"
- **الحالة**: "مُخرج" (لون تحذيري)
- **التفاصيل**: سبب الإخراج والمسؤول

### **في ملخص الأمر:**
- **عدد المنتجات**: عدد الأنواع المُخرجة
- **إجمالي الكمية**: مجموع الكميات المُخرجة
- **سعر الوحدة**: 0.00 (لا توجد قيمة مالية)
- **إجمالي المبلغ**: 0.00 (لا توجد قيمة مالية)

## 🧪 أمثلة الاستخدام

### **مثال 1: منتجات منتهية الصلاحية**
```
نوع الأمر: أمر إخراج
المستودع: المستودع الرئيسي
حالة الإخراج: منتهي الصلاحية
تاريخ الإخراج: 2024-01-15
الشخص المسؤول: أحمد محمد
المنتجات:
- حليب طازج: 10 علب (تاريخ الصلاحية: 2024-01-10)
- زبادي: 5 علب (تاريخ الصلاحية: 2024-01-12)
```

### **مثال 2: منتجات تالفة**
```
نوع الأمر: أمر إخراج
المستودع: مستودع الفرع الأول
حالة الإخراج: تلف/خراب
تاريخ الإخراج: 2024-01-15
الشخص المسؤول: سارة أحمد
ملاحظات: تلف بسبب سقوط من الرف
المنتجات:
- زجاجات عصير: 3 زجاجات
- علب معلبة: 2 علب
```

## 🔍 التحقق والاختبار

### **اختبار 1: إنشاء أمر إخراج**
```
1. افتح صفحة إنشاء أمر جديد
2. اختر "أمر إخراج"
3. تأكد من ظهور حقول الإخراج
4. اختر مستودع
5. اختر حالة إخراج
6. أضف منتجات
7. احفظ الأمر
8. تحقق من خصم المخزون
```

### **اختبار 2: التحقق من البيانات**
```sql
-- التحقق من الأمر
SELECT * FROM receipt_orders WHERE order_type = 'أمر إخراج';

-- التحقق من المنتجات
SELECT * FROM receipt_order_products WHERE receipt_order_id = [ORDER_ID];

-- التحقق من المخزون
SELECT * FROM warehouse_products WHERE warehouse_id = [WAREHOUSE_ID];
```

## 📁 الملفات المحدثة

### **1. الواجهة:**
- `resources/views/receipt_order/create.blade.php`

### **2. الكونترولر:**
- `app/Http/Controllers/ReceiptOrderController.php`

### **3. النموذج:**
- `app/Models/ReceiptOrder.php`

### **4. قاعدة البيانات:**
- `database/migrations/2024_01_15_000001_create_receipt_orders_table.php`
- `run_migrations.sql`

## 🚀 للنشر

### **الخطوة 1: رفع الملفات**
```bash
# الواجهة
scp resources/views/receipt_order/create.blade.php user@server:/path/to/project/resources/views/receipt_order/

# الكونترولر
scp app/Http/Controllers/ReceiptOrderController.php user@server:/path/to/project/app/Http/Controllers/

# النموذج
scp app/Models/ReceiptOrder.php user@server:/path/to/project/app/Models/

# الهجرة
scp database/migrations/2024_01_15_000001_create_receipt_orders_table.php user@server:/path/to/project/database/migrations/
```

### **الخطوة 2: تحديث قاعدة البيانات**
```bash
# إذا كان الجدول موجود، أضف الحقول الجديدة
ALTER TABLE receipt_orders 
ADD COLUMN exit_reason ENUM('فقدان','منتهي الصلاحية','تلف/خراب','بيع بالتجزئة') NULL,
ADD COLUMN exit_date DATE NULL,
ADD COLUMN responsible_person VARCHAR(255) NULL;

# تحديث enum للنوع
ALTER TABLE receipt_orders 
MODIFY COLUMN order_type ENUM('استلام بضاعة','نقل بضاعة','أمر إخراج') NOT NULL;
```

### **الخطوة 3: مسح الكاش**
```bash
ssh user@server "cd /path/to/project && php artisan cache:clear && php artisan view:clear"
```

## ✅ قائمة التحقق النهائية

بعد النشر، تأكد من:

- [ ] **ظهور "أمر إخراج" في قائمة نوع الأمر**
- [ ] **ظهور حقول الإخراج عند الاختيار**
- [ ] **عمل قائمة حالات الإخراج**
- [ ] **إمكانية إضافة منتجات**
- [ ] **خصم المخزون عند الحفظ**
- [ ] **حفظ تفاصيل الإخراج**
- [ ] **ظهور الأمر في القائمة بحالة "مُخرج"**

## 🎉 الفوائد

1. **تتبع دقيق**: تسجيل جميع عمليات الإخراج
2. **أسباب واضحة**: تصنيف أسباب الإخراج
3. **مسؤولية**: تحديد الشخص المسؤول
4. **تقارير شاملة**: إحصائيات مفصلة
5. **إدارة مخزون دقيقة**: خصم تلقائي من المخزون
6. **مرونة**: دعم جميع أنواع الإخراج

الآن النظام يدعم إدارة شاملة لجميع عمليات المستودعات! 🎯
