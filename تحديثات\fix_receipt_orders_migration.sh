#!/bin/bash

# سكريبت إصلاح مشكلة هجرة أوامر الاستلام
# يحل مشكلة errno: 150 في المفاتيح الخارجية

# ألوان للعرض
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# متغيرات - عدل هذه القيم حسب خادمك
SERVER_USER="your_username"
SERVER_HOST="your_server_ip"
PROJECT_PATH="/path/to/your/project"
DB_USER="database_username"
DB_NAME="database_name"

# دالة لطباعة الرسائل الملونة
print_status() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] ⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] ℹ️  $1${NC}"
}

# دالة للتحقق من نجاح الأوامر
check_success() {
    if [ $? -eq 0 ]; then
        print_status "$1 - تم بنجاح"
    else
        print_error "$1 - فشل"
        exit 1
    fi
}

echo -e "${GREEN}"
echo "╔══════════════════════════════════════════════════════════════╗"
echo "║                إصلاح مشكلة هجرة أوامر الاستلام                ║"
echo "║              Fix Receipt Orders Migration Issue              ║"
echo "╚══════════════════════════════════════════════════════════════╝"
echo -e "${NC}"

print_info "بدء عملية إصلاح مشكلة الهجرة..."

# الخطوة 1: التحقق من حالة الهجرة الحالية
print_status "🔍 الخطوة 1: فحص حالة الهجرة الحالية..."
ssh $SERVER_USER@$SERVER_HOST "cd $PROJECT_PATH && php artisan migrate:status | grep receipt" || true

# الخطوة 2: التراجع عن الهجرات الفاشلة
print_warning "⏪ الخطوة 2: التراجع عن الهجرات الفاشلة..."
ssh $SERVER_USER@$SERVER_HOST "cd $PROJECT_PATH && php artisan migrate:rollback --step=5" || true
check_success "التراجع عن الهجرات"

# الخطوة 3: حذف الجداول يدوياً إذا كانت موجودة
print_warning "🗑️ الخطوة 3: تنظيف الجداول الموجودة..."
ssh $SERVER_USER@$SERVER_HOST "cd $PROJECT_PATH && mysql -u $DB_USER -p $DB_NAME -e 'DROP TABLE IF EXISTS receipt_order_products; DROP TABLE IF EXISTS receipt_orders;'" || true
check_success "تنظيف الجداول"

# الخطوة 4: نقل ملفات الهجرة المحدثة
print_status "📁 الخطوة 4: نقل ملفات الهجرة المحدثة..."
scp database/migrations/2024_01_15_000001_create_receipt_orders_table_fixed.php $SERVER_USER@$SERVER_HOST:$PROJECT_PATH/database/migrations/
check_success "نقل ملف هجرة جدول أوامر الاستلام المحدث"

scp database/migrations/2024_01_15_000002_create_receipt_order_products_table_fixed.php $SERVER_USER@$SERVER_HOST:$PROJECT_PATH/database/migrations/
check_success "نقل ملف هجرة جدول منتجات الأوامر المحدث"

scp receipt_orders_migration_fixed.sql $SERVER_USER@$SERVER_HOST:$PROJECT_PATH/
check_success "نقل ملف SQL المحدث"

# الخطوة 5: حذف ملفات الهجرة القديمة
print_warning "🗑️ الخطوة 5: حذف ملفات الهجرة القديمة..."
ssh $SERVER_USER@$SERVER_HOST "cd $PROJECT_PATH && rm -f database/migrations/2024_01_15_000001_create_receipt_orders_table.php" || true
ssh $SERVER_USER@$SERVER_HOST "cd $PROJECT_PATH && rm -f database/migrations/2024_01_15_000002_create_receipt_order_products_table.php" || true
check_success "حذف الملفات القديمة"

# الخطوة 6: إعادة تسمية الملفات الجديدة
print_status "📝 الخطوة 6: إعادة تسمية الملفات الجديدة..."
ssh $SERVER_USER@$SERVER_HOST "cd $PROJECT_PATH && mv database/migrations/2024_01_15_000001_create_receipt_orders_table_fixed.php database/migrations/2024_01_15_000001_create_receipt_orders_table.php"
check_success "إعادة تسمية ملف جدول أوامر الاستلام"

ssh $SERVER_USER@$SERVER_HOST "cd $PROJECT_PATH && mv database/migrations/2024_01_15_000002_create_receipt_order_products_table_fixed.php database/migrations/2024_01_15_000002_create_receipt_order_products_table.php"
check_success "إعادة تسمية ملف جدول منتجات الأوامر"

# الخطوة 7: تشغيل الهجرة الجديدة
print_status "⚡ الخطوة 7: تشغيل الهجرة المحدثة..."
ssh $SERVER_USER@$SERVER_HOST "cd $PROJECT_PATH && php artisan migrate"
check_success "تشغيل الهجرة الجديدة"

# الخطوة 8: التحقق من نجاح الهجرة
print_status "✅ الخطوة 8: التحقق من نجاح الهجرة..."
ssh $SERVER_USER@$SERVER_HOST "cd $PROJECT_PATH && php artisan migrate:status | grep receipt"
check_success "التحقق من حالة الهجرة"

# الخطوة 9: اختبار الجداول
print_status "🧪 الخطوة 9: اختبار الجداول المنشأة..."
ssh $SERVER_USER@$SERVER_HOST "cd $PROJECT_PATH && mysql -u $DB_USER -p $DB_NAME -e 'SHOW TABLES LIKE \"receipt_%\";'"
check_success "اختبار وجود الجداول"

# الخطوة 10: مسح الكاش
print_status "🧹 الخطوة 10: مسح الكاش..."
ssh $SERVER_USER@$SERVER_HOST "cd $PROJECT_PATH && php artisan cache:clear && php artisan view:clear && php artisan route:clear"
check_success "مسح الكاش"

# النتيجة النهائية
echo -e "${GREEN}"
echo "╔══════════════════════════════════════════════════════════════╗"
echo "║                    تم إصلاح المشكلة بنجاح! 🎉                  ║"
echo "╚══════════════════════════════════════════════════════════════╝"
echo -e "${NC}"

print_status "🎯 تم إصلاح مشكلة الهجرة بنجاح!"
print_info "الجداول المنشأة:"
echo -e "${BLUE}   - receipt_orders (جدول أوامر الاستلام)${NC}"
echo -e "${BLUE}   - receipt_order_products (جدول منتجات الأوامر)${NC}"

print_warning "ملاحظة: تم إنشاء الجداول بدون مفاتيح خارجية لتجنب المشاكل"
print_info "يمكن إضافة المفاتيح الخارجية لاحقاً إذا لزم الأمر"

print_status "انتهى الإصلاح في $(date +'%Y-%m-%d %H:%M:%S')"
