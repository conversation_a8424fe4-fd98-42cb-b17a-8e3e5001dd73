<?php
/**
 * اختبار قاعدة البيانات لنظام أوامر الاستلام
 * ضع هذا الملف في مجلد public واستدعه من المتصفح
 */

// إعدادات قاعدة البيانات - عدل هذه القيم حسب إعداداتك
$host = 'localhost';
$dbname = 'your_database_name';
$username = 'your_username';
$password = 'your_password';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h1>🔍 اختبار قاعدة البيانات</h1>";
    echo "<style>
        body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
        table { border-collapse: collapse; width: 100%; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }
        th { background-color: #f2f2f2; }
    </style>";

    // 1. اختبار وجود الجداول
    echo "<div class='section'>";
    echo "<h2>1. فحص الجداول المطلوبة</h2>";
    
    $tables = ['product_services', 'warehouses', 'warehouse_products', 'venders', 'users'];
    foreach ($tables as $table) {
        $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
        if ($stmt->rowCount() > 0) {
            echo "<div class='success'>✅ الجدول $table موجود</div>";
        } else {
            echo "<div class='error'>❌ الجدول $table غير موجود</div>";
        }
    }
    echo "</div>";

    // 2. فحص المنتجات
    echo "<div class='section'>";
    echo "<h2>2. فحص المنتجات</h2>";
    
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM product_services");
    $productCount = $stmt->fetch()['count'];
    echo "<div class='info'>📊 إجمالي المنتجات: $productCount</div>";
    
    if ($productCount > 0) {
        $stmt = $pdo->query("SELECT id, name, sku, sale_price, purchase_price, created_by FROM product_services LIMIT 5");
        echo "<h3>عينة من المنتجات:</h3>";
        echo "<table>";
        echo "<tr><th>ID</th><th>الاسم</th><th>SKU</th><th>سعر البيع</th><th>سعر الشراء</th><th>المنشئ</th></tr>";
        while ($row = $stmt->fetch()) {
            echo "<tr>";
            echo "<td>{$row['id']}</td>";
            echo "<td>{$row['name']}</td>";
            echo "<td>{$row['sku']}</td>";
            echo "<td>{$row['sale_price']}</td>";
            echo "<td>{$row['purchase_price']}</td>";
            echo "<td>{$row['created_by']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    echo "</div>";

    // 3. فحص المستودعات
    echo "<div class='section'>";
    echo "<h2>3. فحص المستودعات</h2>";
    
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM warehouses");
    $warehouseCount = $stmt->fetch()['count'];
    echo "<div class='info'>📊 إجمالي المستودعات: $warehouseCount</div>";
    
    if ($warehouseCount > 0) {
        $stmt = $pdo->query("SELECT id, name, created_by FROM warehouses LIMIT 5");
        echo "<h3>عينة من المستودعات:</h3>";
        echo "<table>";
        echo "<tr><th>ID</th><th>الاسم</th><th>المنشئ</th></tr>";
        while ($row = $stmt->fetch()) {
            echo "<tr>";
            echo "<td>{$row['id']}</td>";
            echo "<td>{$row['name']}</td>";
            echo "<td>{$row['created_by']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    echo "</div>";

    // 4. فحص مخزون المستودعات
    echo "<div class='section'>";
    echo "<h2>4. فحص مخزون المستودعات</h2>";
    
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM warehouse_products");
    $stockCount = $stmt->fetch()['count'];
    echo "<div class='info'>📊 إجمالي سجلات المخزون: $stockCount</div>";
    
    if ($stockCount > 0) {
        $stmt = $pdo->query("
            SELECT wp.warehouse_id, w.name as warehouse_name, 
                   wp.product_id, p.name as product_name, p.sku,
                   wp.quantity, wp.created_by
            FROM warehouse_products wp
            LEFT JOIN warehouses w ON wp.warehouse_id = w.id
            LEFT JOIN product_services p ON wp.product_id = p.id
            LIMIT 10
        ");
        echo "<h3>عينة من المخزون:</h3>";
        echo "<table>";
        echo "<tr><th>مستودع</th><th>منتج</th><th>SKU</th><th>الكمية</th><th>المنشئ</th></tr>";
        while ($row = $stmt->fetch()) {
            echo "<tr>";
            echo "<td>{$row['warehouse_name']} (ID: {$row['warehouse_id']})</td>";
            echo "<td>{$row['product_name']} (ID: {$row['product_id']})</td>";
            echo "<td>{$row['sku']}</td>";
            echo "<td>{$row['quantity']}</td>";
            echo "<td>{$row['created_by']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    echo "</div>";

    // 5. فحص المستخدمين
    echo "<div class='section'>";
    echo "<h2>5. فحص المستخدمين</h2>";
    
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM users");
    $userCount = $stmt->fetch()['count'];
    echo "<div class='info'>📊 إجمالي المستخدمين: $userCount</div>";
    
    if ($userCount > 0) {
        $stmt = $pdo->query("SELECT id, name, email, created_by FROM users LIMIT 5");
        echo "<h3>عينة من المستخدمين:</h3>";
        echo "<table>";
        echo "<tr><th>ID</th><th>الاسم</th><th>البريد</th><th>المنشئ</th></tr>";
        while ($row = $stmt->fetch()) {
            echo "<tr>";
            echo "<td>{$row['id']}</td>";
            echo "<td>{$row['name']}</td>";
            echo "<td>{$row['email']}</td>";
            echo "<td>{$row['created_by']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    echo "</div>";

    // 6. اختبار استعلام تحميل المنتجات
    echo "<div class='section'>";
    echo "<h2>6. اختبار استعلام تحميل المنتجات</h2>";
    
    // جلب أول مستودع
    $stmt = $pdo->query("SELECT id FROM warehouses LIMIT 1");
    $warehouse = $stmt->fetch();
    
    if ($warehouse) {
        $warehouseId = $warehouse['id'];
        echo "<div class='info'>🧪 اختبار المستودع ID: $warehouseId</div>";
        
        // محاكاة استعلام getWarehouseProducts
        $stmt = $pdo->prepare("
            SELECT p.id, p.name, p.sku, p.sale_price, p.purchase_price,
                   COALESCE(wp.quantity, 0) as current_quantity
            FROM product_services p
            LEFT JOIN warehouse_products wp ON p.id = wp.product_id AND wp.warehouse_id = ?
            LIMIT 10
        ");
        $stmt->execute([$warehouseId]);
        $products = $stmt->fetchAll();
        
        echo "<div class='success'>✅ تم جلب " . count($products) . " منتج</div>";
        
        if (count($products) > 0) {
            echo "<h3>المنتجات المتاحة:</h3>";
            echo "<table>";
            echo "<tr><th>ID</th><th>الاسم</th><th>SKU</th><th>سعر البيع</th><th>سعر الشراء</th><th>الكمية الحالية</th></tr>";
            foreach ($products as $product) {
                echo "<tr>";
                echo "<td>{$product['id']}</td>";
                echo "<td>{$product['name']}</td>";
                echo "<td>{$product['sku']}</td>";
                echo "<td>{$product['sale_price']}</td>";
                echo "<td>{$product['purchase_price']}</td>";
                echo "<td>{$product['current_quantity']}</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
    } else {
        echo "<div class='error'>❌ لا توجد مستودعات للاختبار</div>";
    }
    echo "</div>";

    // 7. اختبار البحث
    echo "<div class='section'>";
    echo "<h2>7. اختبار البحث في المنتجات</h2>";
    
    $searchTerm = 'test'; // يمكنك تغيير هذا
    echo "<div class='info'>🔍 البحث عن: '$searchTerm'</div>";
    
    $stmt = $pdo->prepare("
        SELECT id, name, sku, sale_price, purchase_price
        FROM product_services 
        WHERE name LIKE ? OR sku LIKE ?
        LIMIT 5
    ");
    $stmt->execute(["%$searchTerm%", "%$searchTerm%"]);
    $searchResults = $stmt->fetchAll();
    
    echo "<div class='info'>📊 نتائج البحث: " . count($searchResults) . "</div>";
    
    if (count($searchResults) > 0) {
        echo "<table>";
        echo "<tr><th>ID</th><th>الاسم</th><th>SKU</th><th>سعر البيع</th><th>سعر الشراء</th></tr>";
        foreach ($searchResults as $result) {
            echo "<tr>";
            echo "<td>{$result['id']}</td>";
            echo "<td>{$result['name']}</td>";
            echo "<td>{$result['sku']}</td>";
            echo "<td>{$result['sale_price']}</td>";
            echo "<td>{$result['purchase_price']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    echo "</div>";

    echo "<div class='section'>";
    echo "<h2>✅ انتهاء الاختبارات</h2>";
    echo "<div class='success'>تم إجراء جميع الاختبارات بنجاح!</div>";
    echo "<div class='info'>💡 إذا كانت جميع النتائج إيجابية، فالمشكلة قد تكون في:</div>";
    echo "<ul>";
    echo "<li>الصلاحيات (Permissions)</li>";
    echo "<li>المسارات (Routes)</li>";
    echo "<li>JavaScript في المتصفح</li>";
    echo "<li>CSRF Token</li>";
    echo "</ul>";
    echo "</div>";

} catch (PDOException $e) {
    echo "<div class='error'>❌ خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage() . "</div>";
    echo "<div class='info'>💡 تأكد من إعدادات قاعدة البيانات في بداية هذا الملف</div>";
}
?>
