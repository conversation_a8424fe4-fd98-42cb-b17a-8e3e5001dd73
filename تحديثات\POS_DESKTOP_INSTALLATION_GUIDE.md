# دليل تثبيت وتشغيل تطبيق POS سطح المكتب

## 📋 المتطلبات الأساسية

### متطلبات النظام:
- **نظام التشغيل**: Windows 10/11 (64-bit)
- **الذاكرة**: 4GB RAM كحد أدنى، 8GB موصى به
- **مساحة القرص**: 500MB للتطبيق + 2GB للبيانات
- **الشبكة**: اتصال إنترنت للمزامنة مع السيرفر
- **الشاشة**: دقة 1366x768 كحد أدنى

### متطلبات البرمجيات:
- **.NET 6.0 Runtime** أو أحدث
- **Visual C++ Redistributable 2022**
- **MySQL Connector/NET** (يتم تثبيته تلقائياً)

## 🚀 خطوات التثبيت

### الخطوة 1: تحضير البيئة

#### 1.1 تثبيت .NET 6.0 Runtime
```bash
# تحميل من الرابط الرسمي:
https://dotnet.microsoft.com/download/dotnet/6.0

# أو استخدام winget:
winget install Microsoft.DotNet.Runtime.6
```

#### 1.2 تثبيت Visual Studio 2022 (للتطوير)
```bash
# تحميل Visual Studio Community (مجاني):
https://visualstudio.microsoft.com/vs/community/

# المكونات المطلوبة:
- .NET desktop development
- ASP.NET and web development
- Data storage and processing
```

### الخطوة 2: إعداد قاعدة البيانات

#### 2.1 تأكد من تشغيل MySQL Server
```sql
-- تحقق من الاتصال
mysql -u root -p

-- إنشاء قاعدة البيانات (إذا لم تكن موجودة)
CREATE DATABASE IF NOT EXISTS pos_database CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- إنشاء مستخدم للتطبيق (اختياري)
CREATE USER 'pos_user'@'localhost' IDENTIFIED BY 'pos_password';
GRANT ALL PRIVILEGES ON pos_database.* TO 'pos_user'@'localhost';
FLUSH PRIVILEGES;
```

#### 2.2 تحديث سلسلة الاتصال
```xml
<!-- في ملف App.config -->
<connectionStrings>
  <add name="DefaultConnection" 
       connectionString="Server=localhost;Database=pos_database;Uid=root;Pwd=your_password;CharSet=utf8mb4;SslMode=none;" 
       providerName="MySql.Data.MySqlClient" />
</connectionStrings>
```

### الخطوة 3: بناء المشروع

#### 3.1 استنساخ المشروع
```bash
# إنشاء مجلد المشروع
mkdir C:\POS_Desktop_App
cd C:\POS_Desktop_App

# نسخ ملفات المشروع إلى المجلد
```

#### 3.2 استعادة الحزم
```bash
# في مجلد المشروع
dotnet restore
```

#### 3.3 بناء المشروع
```bash
# للتطوير
dotnet build --configuration Debug

# للإنتاج
dotnet build --configuration Release
```

#### 3.4 تشغيل المشروع
```bash
# للتطوير
dotnet run

# أو تشغيل الملف التنفيذي
.\bin\Release\net6.0-windows\POS_Desktop_App.exe
```

## ⚙️ إعداد التطبيق

### الإعدادات الأساسية

#### 1. إعدادات السيرفر
```xml
<appSettings>
  <add key="ServerUrl" value="http://your-server.com:8000" />
  <add key="ApiBaseUrl" value="http://your-server.com:8000/api" />
  <add key="ApiTimeout" value="30" />
</appSettings>
```

#### 2. إعدادات المزامنة
```xml
<appSettings>
  <add key="AutoSync" value="true" />
  <add key="SyncInterval" value="30" />
  <add key="MaxRetryCount" value="3" />
</appSettings>
```

#### 3. إعدادات الطباعة
```xml
<appSettings>
  <add key="DefaultPrinter" value="اسم_الطابعة" />
  <add key="ThermalPrintEnabled" value="true" />
  <add key="AutoPrint" value="false" />
</appSettings>
```

### إعداد الطابعة الحرارية

#### 1. تثبيت تعريف الطابعة
- قم بتوصيل الطابعة الحرارية
- ثبت التعريف المناسب من موقع الشركة المصنعة
- تأكد من ظهور الطابعة في قائمة الطابعات

#### 2. اختبار الطباعة
```csharp
// سيتم إضافة وظيفة اختبار الطباعة في التطبيق
```

## 🔧 استكشاف الأخطاء وإصلاحها

### مشاكل شائعة وحلولها

#### 1. خطأ في الاتصال بقاعدة البيانات
```
خطأ: Unable to connect to any of the specified MySQL hosts
```
**الحل:**
- تأكد من تشغيل MySQL Server
- تحقق من سلسلة الاتصال في App.config
- تأكد من صحة اسم المستخدم وكلمة المرور

#### 2. خطأ في المزامنة
```
خطأ: لا يمكن الوصول إلى السيرفر
```
**الحل:**
- تحقق من اتصال الإنترنت
- تأكد من صحة عنوان السيرفر
- تحقق من إعدادات الجدار الناري

#### 3. خطأ في الطباعة
```
خطأ: الطابعة غير متاحة
```
**الحل:**
- تأكد من توصيل الطابعة
- تحقق من تثبيت التعريف
- اختبر الطباعة من Windows

#### 4. بطء في الأداء
**الحل:**
- تنظيف التخزين المؤقت
- تحديث قاعدة البيانات المحلية
- إعادة تشغيل التطبيق

### ملفات السجلات

#### مواقع ملفات السجلات:
```
C:\Users\<USER>\AppData\Local\POS_Desktop_App\Logs\
```

#### أنواع السجلات:
- **Application.log**: سجل التطبيق العام
- **Database.log**: سجل قاعدة البيانات
- **Sync.log**: سجل المزامنة
- **Print.log**: سجل الطباعة
- **Error.log**: سجل الأخطاء

## 📊 مراقبة الأداء

### مؤشرات الأداء المهمة:
- **استخدام الذاكرة**: يجب ألا يتجاوز 200MB
- **استخدام المعالج**: يجب ألا يتجاوز 10% في الوضع العادي
- **حجم قاعدة البيانات المحلية**: يجب مراقبتها وتنظيفها دورياً
- **سرعة المزامنة**: يجب ألا تتجاوز 30 ثانية

### أدوات المراقبة:
- **مدير المهام**: لمراقبة استخدام الموارد
- **شاشة الأداء المدمجة**: في التطبيق
- **ملفات السجلات**: لتتبع الأخطاء

## 🔄 التحديثات والصيانة

### التحديثات التلقائية:
- يتحقق التطبيق من التحديثات عند بدء التشغيل
- يمكن تعطيل التحديثات التلقائية من الإعدادات
- يتم تنزيل التحديثات في الخلفية

### الصيانة الدورية:
- **يومياً**: نسخ احتياطي تلقائي
- **أسبوعياً**: تنظيف التخزين المؤقت
- **شهرياً**: تحسين قاعدة البيانات المحلية

### النسخ الاحتياطي:
```
مسار النسخ الاحتياطية:
C:\Users\<USER>\AppData\Local\POS_Desktop_App\Backups\
```

## 📞 الدعم الفني

### معلومات الاتصال:
- **البريد الإلكتروني**: <EMAIL>
- **الهاتف**: +966-XX-XXX-XXXX
- **ساعات العمل**: الأحد - الخميس، 9:00 ص - 5:00 م

### قبل الاتصال بالدعم:
1. تحقق من ملفات السجلات
2. جرب إعادة تشغيل التطبيق
3. تأكد من آخر إصدار
4. اجمع معلومات النظام

### معلومات مطلوبة للدعم:
- إصدار التطبيق
- نظام التشغيل
- رسالة الخطأ (إن وجدت)
- خطوات إعادة إنتاج المشكلة

## 🔐 الأمان

### أفضل الممارسات:
- استخدم كلمات مرور قوية
- فعّل التشفير للبيانات المحلية
- حدّث التطبيق بانتظام
- راقب ملفات السجلات للأنشطة المشبوهة

### النسخ الاحتياطي الآمن:
- احفظ النسخ الاحتياطية في مكان آمن
- شفّر النسخ الاحتياطية المهمة
- اختبر استعادة النسخ الاحتياطية دورياً

## 📈 تحسين الأداء

### نصائح لتحسين الأداء:
1. **تنظيف دوري**: احذف البيانات القديمة
2. **تحسين الشبكة**: استخدم اتصال سريع ومستقر
3. **تحديث الأجهزة**: استخدم SSD بدلاً من HDD
4. **إغلاق البرامج غير المستخدمة**: لتوفير الذاكرة

### مراقبة الأداء:
- استخدم شاشة الأداء المدمجة
- راقب استخدام الذاكرة والمعالج
- تتبع أوقات الاستجابة
- راقب حجم قاعدة البيانات المحلية
