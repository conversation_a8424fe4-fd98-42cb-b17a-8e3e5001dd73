-- 🧪 إنشاء بيانات تجريبية لنظام تحليل المبيعات

-- ملاحظة: غير معرف المستخدم والمستودع والعميل حسب بياناتك الفعلية

-- 1. إنشاء فاتورة تجريبية لليوم
INSERT INTO pos (pos_id, customer_id, warehouse_id, pos_date, created_by, created_at, updated_at) 
VALUES 
(1001, 1, 1, CURDATE(), 2, NOW(), NOW()),
(1002, 1, 1, CURDATE(), 2, NOW(), NOW()),
(1003, 1, 1, CURDATE(), 2, NOW(), NOW());

-- 2. إنشاء مدفوعات للفواتير التجريبية
INSERT INTO pos_payments (pos_id, date, amount, created_by, created_at, updated_at)
VALUES 
(LAST_INSERT_ID()-2, CURDATE(), 150.00, 2, NOW(), NOW()),
(LAST_INSERT_ID()-1, CURDATE(), 250.00, 2, NOW(), NOW()),
(LAST_INSERT_ID(), CURDATE(), 300.00, 2, NOW(), NOW());

-- 3. إنشاء فواتير لأيام سابقة في الشهر
INSERT INTO pos (pos_id, customer_id, warehouse_id, pos_date, created_by, created_at, updated_at) 
VALUES 
(1004, 1, 1, DATE_SUB(CURDATE(), INTERVAL 1 DAY), 2, NOW(), NOW()),
(1005, 1, 1, DATE_SUB(CURDATE(), INTERVAL 2 DAY), 2, NOW(), NOW()),
(1006, 1, 1, DATE_SUB(CURDATE(), INTERVAL 3 DAY), 2, NOW(), NOW());

-- 4. إنشاء مدفوعات للفواتير السابقة
INSERT INTO pos_payments (pos_id, date, amount, created_by, created_at, updated_at)
VALUES 
(LAST_INSERT_ID()-2, DATE_SUB(CURDATE(), INTERVAL 1 DAY), 180.00, 2, NOW(), NOW()),
(LAST_INSERT_ID()-1, DATE_SUB(CURDATE(), INTERVAL 2 DAY), 220.00, 2, NOW(), NOW()),
(LAST_INSERT_ID(), DATE_SUB(CURDATE(), INTERVAL 3 DAY), 350.00, 2, NOW(), NOW());

-- 5. إنشاء فواتير لساعات مختلفة اليوم
INSERT INTO pos (pos_id, customer_id, warehouse_id, pos_date, created_by, created_at, updated_at) 
VALUES 
(1007, 1, 1, CURDATE(), 2, CONCAT(CURDATE(), ' 09:00:00'), NOW()),
(1008, 1, 1, CURDATE(), 2, CONCAT(CURDATE(), ' 11:00:00'), NOW()),
(1009, 1, 1, CURDATE(), 2, CONCAT(CURDATE(), ' 14:00:00'), NOW());

-- 6. إنشاء مدفوعات للفواتير بساعات مختلفة
INSERT INTO pos_payments (pos_id, date, amount, created_by, created_at, updated_at)
VALUES 
(LAST_INSERT_ID()-2, CURDATE(), 120.00, 2, NOW(), NOW()),
(LAST_INSERT_ID()-1, CURDATE(), 280.00, 2, NOW(), NOW()),
(LAST_INSERT_ID(), CURDATE(), 190.00, 2, NOW(), NOW());

-- التحقق من البيانات المضافة
SELECT 
    'اليوم' as period,
    COUNT(p.id) as invoices,
    COALESCE(SUM(pp.amount), 0) as total_amount
FROM pos p
LEFT JOIN pos_payments pp ON p.id = pp.pos_id
WHERE p.created_by = 2 
AND DATE(p.pos_date) = CURDATE()

UNION ALL

SELECT 
    'هذا الشهر' as period,
    COUNT(p.id) as invoices,
    COALESCE(SUM(pp.amount), 0) as total_amount
FROM pos p
LEFT JOIN pos_payments pp ON p.id = pp.pos_id
WHERE p.created_by = 2 
AND YEAR(p.pos_date) = YEAR(CURDATE())
AND MONTH(p.pos_date) = MONTH(CURDATE());
