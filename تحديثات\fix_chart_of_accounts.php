<?php
// إصلاح بيانات شجرة الحسابات
echo "<h1>إصلاح بيانات شجرة الحسابات</h1>";

// إعدادات قاعدة البيانات
$host = '127.0.0.1';
$dbname = 'ty';
$username = 'root';
$password = '';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<p style='color: green;'>✓ الاتصال بقاعدة البيانات ناجح</p>";
} catch(PDOException $e) {
    echo "<p style='color: red;'>✗ خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage() . "</p>";
    exit;
}

// قراءة ملف SQL
$sqlFile = 'fix_chart_of_accounts_data.sql';
if (!file_exists($sqlFile)) {
    echo "<p style='color: red;'>✗ ملف SQL غير موجود: $sqlFile</p>";
    exit;
}

$sql = file_get_contents($sqlFile);
$statements = explode(';', $sql);

echo "<h2>تشغيل استعلامات الإصلاح</h2>";

$successCount = 0;
$errorCount = 0;

foreach ($statements as $statement) {
    $statement = trim($statement);
    if (empty($statement) || strpos($statement, '--') === 0) {
        continue; // تجاهل التعليقات والأسطر الفارغة
    }
    
    try {
        $pdo->exec($statement);
        $successCount++;
        echo "<p style='color: green;'>✓ تم تنفيذ الاستعلام بنجاح</p>";
    } catch(PDOException $e) {
        $errorCount++;
        echo "<p style='color: orange;'>⚠ تحذير في الاستعلام: " . $e->getMessage() . "</p>";
    }
}

echo "<h2>نتائج الإصلاح</h2>";
echo "<p>الاستعلامات الناجحة: $successCount</p>";
echo "<p>الأخطاء/التحذيرات: $errorCount</p>";

// فحص البيانات بعد الإصلاح
echo "<h2>فحص البيانات بعد الإصلاح</h2>";

try {
    // عد أنواع الحسابات
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM chart_of_account_types");
    $typesCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    echo "<p>أنواع الحسابات: $typesCount</p>";
    
    // عد الأنواع الفرعية
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM chart_of_account_sub_types");
    $subTypesCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    echo "<p>الأنواع الفرعية: $subTypesCount</p>";
    
    // عد الحسابات
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM chart_of_accounts");
    $accountsCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    echo "<p>الحسابات: $accountsCount</p>";
    
    if ($typesCount > 0 && $subTypesCount > 0 && $accountsCount > 0) {
        echo "<p style='color: green; font-weight: bold;'>✓ تم إصلاح البيانات بنجاح!</p>";
        echo "<p><a href='http://localhost/chart-of-account' target='_blank' style='background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>اختبار شجرة الحسابات</a></p>";
    } else {
        echo "<p style='color: red;'>✗ لا تزال هناك مشكلة في البيانات</p>";
    }
    
} catch(PDOException $e) {
    echo "<p style='color: red;'>✗ خطأ في فحص البيانات: " . $e->getMessage() . "</p>";
}

// عرض عينة من البيانات
echo "<h2>عينة من البيانات المدرجة</h2>";

try {
    echo "<h3>أنواع الحسابات:</h3>";
    $stmt = $pdo->query("SELECT id, name, created_by FROM chart_of_account_types LIMIT 5");
    $types = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (count($types) > 0) {
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>ID</th><th>الاسم</th><th>منشئ بواسطة</th></tr>";
        foreach ($types as $type) {
            echo "<tr><td>{$type['id']}</td><td>{$type['name']}</td><td>{$type['created_by']}</td></tr>";
        }
        echo "</table>";
    }
    
    echo "<h3>الحسابات:</h3>";
    $stmt = $pdo->query("SELECT id, code, name, type, is_enabled, created_by FROM chart_of_accounts LIMIT 5");
    $accounts = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (count($accounts) > 0) {
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>ID</th><th>الكود</th><th>الاسم</th><th>النوع</th><th>مفعل</th><th>منشئ بواسطة</th></tr>";
        foreach ($accounts as $account) {
            echo "<tr>";
            echo "<td>{$account['id']}</td>";
            echo "<td>{$account['code']}</td>";
            echo "<td>{$account['name']}</td>";
            echo "<td>{$account['type']}</td>";
            echo "<td>" . ($account['is_enabled'] ? 'نعم' : 'لا') . "</td>";
            echo "<td>{$account['created_by']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
} catch(PDOException $e) {
    echo "<p style='color: red;'>✗ خطأ في عرض البيانات: " . $e->getMessage() . "</p>";
}

echo "<h2>الخطوات التالية</h2>";
echo "<div style='background-color: #f0f8ff; padding: 15px; border-left: 4px solid #007bff;'>";
echo "<ol>";
echo "<li>تأكد من تسجيل الدخول إلى النظام</li>";
echo "<li>تحقق من أن لديك صلاحية 'manage chart of account'</li>";
echo "<li>اذهب إلى صفحة شجرة الحسابات: <a href='http://localhost/chart-of-account' target='_blank'>http://localhost/chart-of-account</a></li>";
echo "<li>إذا لم تظهر البيانات، تحقق من قيمة created_by في قاعدة البيانات</li>";
echo "</ol>";
echo "</div>";

echo "<br><button onclick='location.reload()' style='padding: 10px 20px; background-color: #28a745; color: white; border: none; border-radius: 5px; cursor: pointer;'>إعادة تشغيل الإصلاح</button>";
echo " <a href='debug_chart_simple.php' style='padding: 10px 20px; background-color: #6c757d; color: white; text-decoration: none; border-radius: 5px; margin-left: 10px;'>فحص البيانات</a>";
?>
