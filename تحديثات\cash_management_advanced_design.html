<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة النقد المتقدمة</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.css" rel="stylesheet">
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background-color: #f8f9fa; }
        .header-section { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px 0; margin-bottom: 30px; }
        .filter-card { background: white; border-radius: 15px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); padding: 20px; margin-bottom: 25px; }
        .stats-card { background: white; border-radius: 15px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); padding: 20px; margin-bottom: 20px; text-align: center; }
        .stats-card.success { border-left: 5px solid #28a745; }
        .stats-card.danger { border-left: 5px solid #dc3545; }
        .stats-card.warning { border-left: 5px solid #ffc107; }
        .stats-card.info { border-left: 5px solid #17a2b8; }
        .table-card { background: white; border-radius: 15px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); padding: 20px; margin-bottom: 25px; }
        .table-responsive { border-radius: 10px; }
        .table th { background-color: #f8f9fa; border-top: none; font-weight: 600; }
        .badge-active { background-color: #28a745; }
        .badge-closed { background-color: #6c757d; }
        .chart-container { height: 300px; }
        .alert-custom { border-radius: 10px; border: none; }
    </style>
</head>
<body>
    <!-- Header Section -->
    <div class="header-section">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h2><i class="fas fa-cash-register me-3"></i>إدارة النقد المتقدمة</h2>
                    <p class="mb-0">مراقبة شاملة للشفتات وحركة النقد ومبيعات نقاط البيع</p>
                </div>
                <div class="col-md-4 text-end">
                    <button class="btn btn-light btn-lg">
                        <i class="fas fa-sync-alt me-2"></i>تحديث البيانات
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="container-fluid">
        <!-- Filters Section -->
        <div class="filter-card">
            <div class="row">
                <div class="col-md-3">
                    <label class="form-label"><i class="fas fa-warehouse me-2"></i>المستودع</label>
                    <select class="form-select">
                        <option>جميع المستودعات</option>
                        <option>المستودع الرئيسي</option>
                        <option>فرع الرياض</option>
                        <option>فرع جدة</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label"><i class="fas fa-user me-2"></i>المستخدم</label>
                    <select class="form-select">
                        <option>جميع المستخدمين</option>
                        <option>أحمد محمد</option>
                        <option>فاطمة علي</option>
                        <option>محمد سالم</option>
                    </select>
                </div>
                <div class="col-md-4">
                    <label class="form-label"><i class="fas fa-calendar me-2"></i>فترة التاريخ</label>
                    <input type="text" class="form-control" id="daterange" value="01/01/2024 - 31/12/2024">
                </div>
                <div class="col-md-2 d-flex align-items-end">
                    <button class="btn btn-primary w-100">
                        <i class="fas fa-search me-2"></i>بحث
                    </button>
                </div>
            </div>
        </div>

        <!-- Quick Stats -->
        <div class="row">
            <div class="col-md-3">
                <div class="stats-card success">
                    <h3 class="text-success">125,450.00</h3>
                    <p class="mb-0">إجمالي المقبوضات اليوم</p>
                    <small class="text-muted">ريال سعودي</small>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card danger">
                    <h3 class="text-danger">45,200.00</h3>
                    <p class="mb-0">إجمالي المصروفات اليوم</p>
                    <small class="text-muted">ريال سعودي</small>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card info">
                    <h3 class="text-info">80,250.00</h3>
                    <p class="mb-0">صافي النقد اليومي</p>
                    <small class="text-muted">ريال سعودي</small>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card warning">
                    <h3 class="text-warning">3</h3>
                    <p class="mb-0">شفتات مفتوحة</p>
                    <small class="text-muted">تحتاج مراجعة</small>
                </div>
            </div>
        </div>

        <!-- Alerts Section -->
        <div class="row">
            <div class="col-12">
                <div class="alert alert-warning alert-custom">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>تنبيه:</strong> يوجد شفت مفتوح منذ أكثر من 12 ساعة في المستودع الرئيسي
                </div>
            </div>
        </div>

        <!-- Main Content Tabs -->
        <div class="table-card">
            <ul class="nav nav-tabs" id="mainTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="shifts-tab" data-bs-toggle="tab" data-bs-target="#shifts" type="button">
                        <i class="fas fa-clock me-2"></i>الشفتات
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="receipts-tab" data-bs-toggle="tab" data-bs-target="#receipts" type="button">
                        <i class="fas fa-arrow-down me-2"></i>سندات القبض
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="payments-tab" data-bs-toggle="tab" data-bs-target="#payments" type="button">
                        <i class="fas fa-arrow-up me-2"></i>سندات الصرف
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="pos-tab" data-bs-toggle="tab" data-bs-target="#pos" type="button">
                        <i class="fas fa-cash-register me-2"></i>مبيعات POS
                    </button>
                </li>
            </ul>

            <div class="tab-content" id="mainTabsContent">
                <!-- Shifts Tab -->
                <div class="tab-pane fade show active" id="shifts" role="tabpanel">
                    <div class="table-responsive mt-3">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>رقم الشفت</th>
                                    <th>المستخدم</th>
                                    <th>المستودع</th>
                                    <th>وقت البدء</th>
                                    <th>وقت الانتهاء</th>
                                    <th>الرصيد الافتتاحي</th>
                                    <th>النقد الحالي</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>#SH001</td>
                                    <td>أحمد محمد</td>
                                    <td>المستودع الرئيسي</td>
                                    <td>08:00 AM</td>
                                    <td>-</td>
                                    <td>5,000.00</td>
                                    <td>12,450.00</td>
                                    <td><span class="badge badge-active">نشط</span></td>
                                    <td>
                                        <button class="btn btn-sm btn-primary"><i class="fas fa-eye"></i></button>
                                        <button class="btn btn-sm btn-warning"><i class="fas fa-edit"></i></button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>#SH002</td>
                                    <td>فاطمة علي</td>
                                    <td>فرع الرياض</td>
                                    <td>09:00 AM</td>
                                    <td>05:00 PM</td>
                                    <td>3,000.00</td>
                                    <td>8,750.00</td>
                                    <td><span class="badge badge-closed">مغلق</span></td>
                                    <td>
                                        <button class="btn btn-sm btn-primary"><i class="fas fa-eye"></i></button>
                                        <button class="btn btn-sm btn-info"><i class="fas fa-print"></i></button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Receipt Vouchers Tab -->
                <div class="tab-pane fade" id="receipts" role="tabpanel">
                    <div class="table-responsive mt-3">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>رقم السند</th>
                                    <th>التاريخ</th>
                                    <th>المستلم</th>
                                    <th>القيمة</th>
                                    <th>النوع</th>
                                    <th>الملاحظات</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>#RC001</td>
                                    <td>2024-01-15</td>
                                    <td>أحمد محمد</td>
                                    <td>2,500.00</td>
                                    <td><span class="badge bg-success">نقد</span></td>
                                    <td>دفعة من العميل</td>
                                    <td>
                                        <button class="btn btn-sm btn-primary"><i class="fas fa-eye"></i></button>
                                        <button class="btn btn-sm btn-warning"><i class="fas fa-edit"></i></button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Payment Vouchers Tab -->
                <div class="tab-pane fade" id="payments" role="tabpanel">
                    <div class="table-responsive mt-3">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>رقم السند</th>
                                    <th>التاريخ</th>
                                    <th>المستفيد</th>
                                    <th>القيمة</th>
                                    <th>النوع</th>
                                    <th>الملاحظات</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>#PY001</td>
                                    <td>2024-01-15</td>
                                    <td>شركة التوريد</td>
                                    <td>1,200.00</td>
                                    <td><span class="badge bg-info">تحويل</span></td>
                                    <td>دفع فاتورة</td>
                                    <td>
                                        <button class="btn btn-sm btn-primary"><i class="fas fa-eye"></i></button>
                                        <button class="btn btn-sm btn-warning"><i class="fas fa-edit"></i></button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- POS Sales Tab -->
                <div class="tab-pane fade" id="pos" role="tabpanel">
                    <div class="row mt-3">
                        <div class="col-md-8">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>التاريخ</th>
                                            <th>المستخدم</th>
                                            <th>عدد الفواتير</th>
                                            <th>إجمالي النقد</th>
                                            <th>إجمالي البطاقة</th>
                                            <th>الإجمالي الكلي</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>2024-01-15</td>
                                            <td>أحمد محمد</td>
                                            <td>25</td>
                                            <td>8,500.00</td>
                                            <td>3,200.00</td>
                                            <td>11,700.00</td>
                                        </tr>
                                        <tr>
                                            <td>2024-01-15</td>
                                            <td>فاطمة علي</td>
                                            <td>18</td>
                                            <td>6,200.00</td>
                                            <td>2,800.00</td>
                                            <td>9,000.00</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="chart-container">
                                <canvas id="salesChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/moment/moment.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.min.js"></script>
    <script>
        // Initialize date range picker
        $('#daterange').daterangepicker({
            startDate: moment().subtract(29, 'days'),
            endDate: moment(),
            locale: {
                format: 'DD/MM/YYYY',
                separator: ' - ',
                applyLabel: 'تطبيق',
                cancelLabel: 'إلغاء',
                fromLabel: 'من',
                toLabel: 'إلى',
                customRangeLabel: 'مخصص',
                weekLabel: 'أ',
                daysOfWeek: ['أح', 'إث', 'ث', 'أر', 'خ', 'ج', 'س'],
                monthNames: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'],
                firstDay: 1
            }
        });

        // Initialize chart
        const ctx = document.getElementById('salesChart').getContext('2d');
        const salesChart = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: ['نقد', 'بطاقة ائتمان', 'تحويل'],
                datasets: [{
                    data: [14700, 6000, 2000],
                    backgroundColor: ['#28a745', '#17a2b8', '#ffc107'],
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    </script>
</body>
</html>
